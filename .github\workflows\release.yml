name: Make Release
on: workflow_dispatch

permissions:
  contents: read

concurrency:
  group: stable-release
  cancel-in-progress: true

jobs:
  make-release:
    permissions: write-all
    runs-on: windows-2022

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
          fetch-depth: 0 # needed to get commits since last tag
          token: ${{ secrets.UEPSEUDO_PAT }}

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      # Specifically use MSVC toolset v19.39.33523
      - name: Install VS2022 BuildTools 17.9.7
        run: choco install -y visualstudio2022buildtools --version=********* --params "--add Microsoft.VisualStudio.Component.VC.Tools.x86.x64 --installChannelUri https://aka.ms/vs/17/release/180911598_-255012421/channel"

      - name: Setup xmake
        uses: xmake-io/github-action-setup-xmake@v1
        with:
          xmake-version: '2.9.3'

      - name: Release Commit
        id: release_commit
        run: python release.py release_commit ${{ github.actor }}
        working-directory: ./tools/buildscripts

      - name: C<PERSON>
        uses: actions/cache@v4
        with:
          path: |
            .xmake
            Binaries
            Intermediates
            C:/Users/<USER>/AppData/Local/.xmake
          key: ${{ runner.os }}-xmake-${{ hashFiles('**/xmake.lua') }}

      - name: Build
        run: |
          xmake f -m "Game__Shipping__Win64" -y
          xmake build

      - name: Package
        id: package
        run: python tools/buildscripts/release.py package

      - name: Push changes
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ github.ref }}
          tags: true

      - name: Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.release_commit.outputs.release_tag }}
          body_path: release/release_notes.md
          files: |
            release/UE4SS_v*.zip
            release/zDEV-UE4SS_v*.zip
            release/zCustomGameConfigs.zip
            release/zMapGenBP.zip
