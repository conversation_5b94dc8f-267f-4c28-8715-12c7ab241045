﻿[Overrides]
; Path to the 'Mods' folder
; Default: <dll_directory>/Mods
ModsFolderPath =

[General]
; Whether to reload all mods when the key defined by <PERSON>Reload<PERSON><PERSON> is hit.
; Default: 1
EnableHotReloadSystem = 0

; The key that will trigger a reload of all mods.
; The CTRL key is always required.
; Valid values (case-insensitive): Anything from Mods/Keybinds/Scripts/main.lua
; Default: R
HotReloadKey = R

; Whether caches will be invalidated if ue4ss.dll has changed
; Default: 1
InvalidateCacheIfDLLDiffers = 1

; The maximum number attempts the scanner will try before erroring out if an aob isn't found
; Default: 60
MaxScanAttemptsNormal = 60

; The maximum number attempts the scanner will try for modular games before erroring out if an aob isn't found
; Default: 2000
MaxScanAttemptsModular = 2500

[EngineVersionOverride]
MajorVersion = 4
MinorVersion = 27

[ObjectDumper]
; Whether to force all assets to be loaded before dumping objects
; WARNING: Can require multiple gigabytes of extra memory
; WARNING: Is not stable & will crash the game if you load past the main menu after dumping
; Default: 0
LoadAllAssetsBeforeDumpingObjects = 0

; Whether to display the offset from the main executable for functions instead of the function pointer
; Default: 0
UseModuleOffsets = 0

[CXXHeaderGenerator]
; Whether to property offsets and sizes
; Default: 0
DumpOffsetsAndSizes = 1

; Whether memory layouts of classes and structs should be accurate
; This must be set to 1, if you want to use the generated headers in an actual C++ project
; When set to 0, padding member variables will not be generated
; NOTE: A VALUE OF 1 HAS NO PURPOSE YET! MEMORY LAYOUT IS NOT ACCURATE EITHER WAY!
; Default: 0
KeepMemoryLayout = 0

; Whether to force all assets to be loaded before generating headers
; WARNING: Can require multiple gigabytes of extra memory
; WARNING: Is not stable & will crash the game if you load past the main menu after dumping
; Default: 0
LoadAllAssetsBeforeGeneratingCXXHeaders = 0

[UHTHeaderGenerator]
; Whether to skip generating packages that belong to the engine
; Some games make alterations to the engine and for those games you might want to set this to 0
; Default: 0
IgnoreAllCoreEngineModules = 1

; Whether to skip generating the "Engine" and "CoreUObject" packages
; Default: 1
IgnoreEngineAndCoreUObject = 1

; Whether to force all UFUNCTION macros to have "BlueprintCallable"
; Note: This will cause some errors in the generated headers that you will need to manually fix
; Default: 0
MakeAllFunctionsBlueprintCallable = 0

; Whether to force all UPROPERTY macros to have 'BlueprintReadWrite'
; Also forces all UPROPERTY macros to have 'meta=(AllowPrivateAccess=true)'
; Default: 0
MakeAllPropertyBlueprintsReadWrite = 0

; Whether to force UENUM macros on enums to have 'BlueprintType' if the underlying type was implicit or uint8
; Note: This also forces the underlying type to be uint8 where the type would otherwise be implicit
; Default: 0
MakeEnumClassesBlueprintType = 0

[Debug]
; Whether to enable the external UE4SS debug console.
ConsoleEnabled = 1

[Threads]
; The number of threads that the sig scanner will use (not real cpu threads, can be over your physical & hyperthreading max)
; If the game is modular then multi-threading will always be off regardless of the settings in this file
; Min: 1
; Max: 4294967295
; Default: 8
SigScannerNumThreads = 8

; The minimum size that a module has to be in order for multi-threading to be enabled
; This should be large enough so that the cost of creating threads won't out-weigh the speed gained from scanning in multiple threads
; Min: 0
; Max: 4294967295
; Default: 16777216
SigScannerMultithreadingModuleSizeThreshold = 16777216

[Hooks]
FExecVTableOffsetInLocalPlayer = 0x30