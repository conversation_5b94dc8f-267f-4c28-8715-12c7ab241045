name: Make Experimental Release

on:
  workflow_dispatch:
  push:
    branches: [ "main" ]
    paths:
      - "UE4SS/src/**"
      - "UE4SS/include/**"
      - "UE4SS/generated_src/**"
      - "UE4SS/generated_include/**"
      - "deps/**"
      - "UE4SS/proxy_generator/**"
      - "assets/Mods/**"
      - "assets/**.ini"

permissions:
  contents: read

concurrency:
  group: experimental-release
  cancel-in-progress: true

jobs:
  make-release:
    permissions: write-all
    runs-on: windows-2022

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
          fetch-depth: 0 # needed to get commits since last tag
          token: ${{ secrets.UEPSEUDO_PAT }}

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      # Specifically use MSVC toolset v19.39.33523
      - name: Install VS2022 BuildTools 17.9.7
        run: choco install -y visualstudio2022buildtools --version=117.9.7.0 --params "--add Microsoft.VisualStudio.Component.VC.Tools.x86.x64 --installChannel<PERSON>ri https://aka.ms/vs/17/release/180911598_-255012421/channel"

      - name: Setup xmake
        uses: xmake-io/github-action-setup-xmake@v1
        with:
          xmake-version: '2.9.3'

      - name: Cache
        uses: actions/cache@v4
        with:
          path: |
            .xmake
            Binaries
            Intermediates
            C:/Users/<USER>/AppData/Local/.xmake
          key: ${{ runner.os }}-xmake-${{ hashFiles('**/xmake.lua') }}

      - name: Build
        run: |
          xmake f -m "Game__Shipping__Win64" -y
          xmake build

      - name: Package
        run: python tools/buildscripts/release.py package -e

      - name: Make Archival Experimental Release
        uses: softprops/action-gh-release@v1
        with:
          prerelease: true
          tag_name: experimental
          body_path: release/release_notes.md
          files: |
            release/UE4SS_v*.zip
            release/zDEV-UE4SS_v*.zip
            release/zCustomGameConfigs.zip
            release/zMapGenBP.zip

      - name: Delete old release assets
        uses: mknejp/delete-release-assets@v1
        with:
          token: ${{ github.token }}
          tag: experimental-latest
          assets: '*'

      - name: Make Permanent Latest Release
        uses: softprops/action-gh-release@v1
        with:
          prerelease: true
          tag_name: experimental-latest
          body_path: release/release_notes.md
          files: |
            release/UE4SS_v*.zip
            release/zDEV-UE4SS_v*.zip
            release/zCustomGameConfigs.zip
            release/zMapGenBP.zip
