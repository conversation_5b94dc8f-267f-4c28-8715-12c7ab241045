[UEnum]
CppForm = 0x50
CppType = 0x30
EnumDisplayNameFn = 0x58
EnumFlags = 0x54
EnumPackage = 0x60
Names = 0x40

[FDelegateProperty]
SignatureFunction = 0x70

[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20

[FSetProperty]
ElementProp = 0x70

[UClass]
AllFunctionsCache = 0x280
ClassConfigName = 0x144
ClassConstructor = 0xB0
ClassDefaultObject = 0x170
ClassFlags = 0xD4
ClassUnique = 0xC8
ClassVTableHelperCtorCaller = 0xB8
ClassWithin = 0xE0
FirstOwnedClassRep = 0xCC
FuncMap = 0x188
GenericFuncPtrMap = 0xE8
Interfaces = 0x2D8
NetFields = 0x160
ReferenceSchema = 0x2E8
ScriptTypePtr = 0x138
SparseClassData = 0x178
SparseClassDataStruct = 0x180
bCooked = 0xD0
bIsScriptClass = 0x140
bLayoutChanging = 0xD1

[UDataTable]
ImportKeyField = 0x88
RowMap = 0x30
RowStruct = 0x28
bIgnoreExtraFields = 0x80
bIgnoreMissingFields = 0x80
bStripFromClientBuilds = 0x80

[FArchiveState]
ArAllowLazyLoading = 0x2A
ArContainsCode = 0x29
ArContainsMap = 0x29
ArCustomPropertyList = 0x60
ArEngineVer = 0x4C
ArForceByteSwapping = 0x2A
ArForceUnicode = 0x28
ArIgnoreArchetypeRef = 0x2A
ArIgnoreClassGeneratedByRef = 0x2A
ArIgnoreClassRef = 0x2A
ArIgnoreOuterRef = 0x2A
ArIsCountingMemory = 0x2B
ArIsCriticalError = 0x29
ArIsError = 0x29
ArIsFilterEditorOnly = 0x2B
ArIsLoading = 0x28
ArIsLoadingFromCookedPackage = 0x28
ArIsModifyingWeakAndStrongReferences = 0x2B
ArIsNetArchive = 0x2B
ArIsObjectReferenceCollector = 0x2B
ArIsPersistent = 0x29
ArIsSaveGame = 0x2B
ArIsSaving = 0x28
ArIsTextFormat = 0x28
ArIsTransacting = 0x28
ArLicenseeUEVer = 0x48
ArMaxSerializeSize = 0x38
ArNoDelta = 0x2A
ArNoIntraPropertyDelta = 0x2A
ArPortFlags = 0x30
ArRequiresLocalizationGather = 0x29
ArSerializingDefaults = 0x2C
ArShouldSkipBulkData = 0x2B
ArShouldSkipCompilingAssets = 0x29
ArShouldSkipUpdateCustomVersion = 0x29
ArUEVer = 0x40
ArUseCustomPropertyList = 0x2B
ArUseUnversionedPropertySerialization = 0x28
ArWantBinaryPropertySerialization = 0x28
CustomVersionContainer = 0x58
NextProxy = 0x88
SerializedProperty = 0x70
bCustomVersionsAreReset = 0x80

[AGameModeBase]
DefaultPlayerName = 0x300
GameNetDriverReplicationSystem = 0x314
GameSession = 0x2E8
GameSessionClass = 0x2A0
HUDClass = 0x2C0
OptionsString = 0x290
PlayerStateClass = 0x2B8
ServerStatReplicator = 0x2F8
ServerStatReplicatorClass = 0x2E0
SpectatorClass = 0x2D0
bPauseable = 0x310
bStartPlayersAsSpectators = 0x310
bUseSeamlessTravel = 0x310

[FSoftClassProperty]
MetaClass = 0x78

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[FMulticastDelegateProperty]
SignatureFunction = 0x70

[FInterfaceProperty]
InterfaceClass = 0x70

[UFunction]
EventGraphCallOffset = 0xD8
EventGraphFunction = 0xD0
FirstPropertyToInit = 0xC8
Func = 0xE0
FunctionFlags = 0xB0
NumParms = 0xB8
ParmsSize = 0xBA
RPCId = 0xC2
RPCResponseId = 0xC4
ReturnValueOffset = 0xBC

[UField]
Next = 0x28

[FBoolProperty]
ByteMask = 0x72
ByteOffset = 0x71
FieldMask = 0x73
FieldSize = 0x70

[FField]
ClassPrivate = 0x8
FlagsPrivate = 0x28
NamePrivate = 0x20
Next = 0x18
Owner = 0x10

[FWorldContext]
AudioDeviceID = 0x240
ContextHandle = 0xA0
CustomDescription = 0x248
ExternalReferences = 0x2B0
GameViewport = 0x200
LevelsToLoadForPendingMapChange = 0x1A8
PIEAccumulatedTickSeconds = 0x25C
PIEFixedTickSeconds = 0x258
PIEInstance = 0x220
PIEPrefix = 0x228
PendingMapChangeFailureDescription = 0x1C8
RunAsDedicated = 0x23C
ThisCurrentWorld = 0x2C0
TravelType = 0xB8
TravelURL = 0xA8
bIsPrimaryPIEInstance = 0x23E
bShouldCommitPendingMapChange = 0x1D8
bWaitingOnOnlineSubsystem = 0x23D

[ULocalPlayer]
AspectRatioAxisConstraint = 0xB8
CachedUniqueNetId = 0x48
ControllerId = 0xE0
OnPlayerControllerChangedEvent = 0x120
PlatformUserId = 0x100
SlateOperations = 0x1F8
ViewportClient = 0x78
bSentSplitJoin = 0xC8

[FByteProperty]
Enum = 0x70

[FProperty]
ArrayDim = 0x30
DestructorLinkNext = 0x58
ElementSize = 0x34
NextRef = 0x50
Offset_Internal = 0x44
PostConstructLinkNext = 0x60
PropertyFlags = 0x38
PropertyLinkNext = 0x48
RepIndex = 0x40
RepNotifyFunc = 0x68

[UStruct]
ChildProperties = 0x50
Children = 0x48
DestructorLink = 0x90
MinAlignment = 0x5C
PostConstructLink = 0x98
PropertiesSize = 0x58
PropertyLink = 0x70
RefLink = 0x78
Script = 0x60
ScriptAndPropertyObjectReferences = 0xA8
SuperStruct = 0x40
UnresolvedScriptProperties = 0xA0

[FEnumProperty]
Enum = 0x78
UnderlyingProp = 0x70

[FMapProperty]
KeyProp = 0x70
MapFlags = 0x98
ValueProp = 0x78

[FClassProperty]
MetaClass = 0x78

[FObjectPropertyBase]
PropertyClass = 0x70

[UScriptStruct]
CppStructOps = 0xB8
StructFlags = 0xB0
bIsScriptStruct = 0xB4
bPrepareCppStructOpsCompleted = 0xB5

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[FArrayProperty]
ArrayFlags = 0x70
Inner = 0x78

[FStructProperty]
Struct = 0x70

[FFieldPathProperty]
PropertyClass = 0x70

[AActor]
ActorCategory = 0x61
ActorHasBegunPlay = 0x5D
AttachmentReplication = 0x70
AutoReceiveInput = 0x153
Children = 0x190
CreationTime = 0x158
CustomTimeDilation = 0x68
DefaultUpdateOverlapsMethodDuringLevelStreaming = 0x5F
DetachFence = 0x288
InitialLifeSpan = 0x64
InputComponent = 0x160
InputPriority = 0x154
LastRenderTime = 0x180
Layers = 0x1B0
MinNetUpdateFrequency = 0x174
NetCullDistanceSquared = 0x168
NetDormancy = 0x151
NetDriverName = 0x148
NetPriority = 0x178
NetTag = 0x16C
NetUpdateFrequency = 0x170
OnActorBeginOverlap = 0x1DB
OnActorEndOverlap = 0x1DC
OnActorHit = 0x1E5
OnBeginCursorOver = 0x1DD
OnClicked = 0x1DF
OnDestroyed = 0x1E6
OnEndCursorOver = 0x1DE
OnEndPlay = 0x1E7
OnInputTouchBegin = 0x1E1
OnInputTouchEnd = 0x1E2
OnInputTouchEnter = 0x1E3
OnInputTouchLeave = 0x1E4
OnReleased = 0x1E0
OnTakeAnyDamage = 0x1D8
OnTakePointDamage = 0x1D9
OnTakeRadialDamage = 0x1DA
Owner = 0x140
ParentComponent = 0x1C0
PhysicsReplicationMode = 0x17C
PrimaryActorTick = 0x28
RayTracingGroupId = 0x6C
RemoteRole = 0x60
ReplicatedComponentsInfo = 0x1F8
ReplicatedMovement = 0xD0
ReplicatedSubObjects = 0x1E8
Role = 0x150
RootComponent = 0x1A0
SpawnCollisionHandlingMethod = 0x152
Tags = 0x1C8
TimerHandle_LifeSpanExpired = 0x1A8
UpdateOverlapsMethodDuringLevelStreaming = 0x5E
bActorBeginningPlayFromLevelStreaming = 0x5C
bActorEnableCollision = 0x5C
bActorInitialized = 0x5C
bActorIsBeingConstructed = 0x5D
bActorIsBeingDestroyed = 0x5D
bActorSeamlessTraveled = 0x5B
bActorWantsDestroyDuringBeginPlay = 0x5D
bAllowReceiveTickEventOnDedicatedServer = 0x5B
bAllowTickBeforeBeginPlay = 0x5A
bAlwaysRelevant = 0x58
bAsyncPhysicsTickEnabled = 0x5D
bAutoDestroyWhenFinished = 0x5A
bBlockInput = 0x5A
bCallPreReplication = 0x58
bCallPreReplicationForReplay = 0x58
bCanBeDamaged = 0x5A
bCanBeInCluster = 0x5B
bCollideWhenPlacing = 0x5A
bEnableAutoLODGeneration = 0x5B
bExchangedRoles = 0x59
bFindCameraComponentWhenViewTarget = 0x5A
bForceNetAddressable = 0x59
bGenerateOverlapEventsDuringLevelStreaming = 0x5A
bHasDeferredComponentRegistration = 0x5C
bHasFinishedSpawning = 0x5C
bHasRegisteredAllComponents = 0x5C
bHidden = 0x58
bIgnoresOriginShifting = 0x5A
bIsEditorOnlyActor = 0x5B
bNetCheckedInitialPhysicsState = 0x5B
bNetLoadOnClient = 0x59
bNetStartup = 0x58
bNetTemporary = 0x58
bNetUseOwnerRelevancy = 0x59
bOnlyRelevantToOwner = 0x58
bRelevantForLevelBounds = 0x59
bRelevantForNetworkReplays = 0x59
bReplayRewindable = 0x59
bReplicateMovement = 0x58
bReplicateUsingRegisteredSubObjectList = 0x5B
bReplicates = 0x5B
bRunningUserConstructionScript = 0x5C
bTearOff = 0x59
bTickFunctionsRegistered = 0x5C

[UPlayer]
ConfiguredInternetSpeed = 0x3C
ConfiguredLanSpeed = 0x40
CurrentNetSpeed = 0x38

[UWorld]
ActiveLevelCollectionIndex = 0x198
AllLevelsChangedEvent = 0x540
AudioTimeSeconds = 0x6D8
AuthorityGameMode = 0x158
BlockTillLevelStreamingCompletedEpoch = 0x148
BuildStreamingDataTimer = 0x4A8
CleanupWorldTag = 0x76C
CommittedPersistentLevelName = 0x760
DeltaRealTimeSeconds = 0x6E0
DeltaTimeSeconds = 0x6E4
ExtraReferencedObjects = 0x68
IsInBlockTillLevelStreamingCompleted = 0x144
LastRenderTime = 0x130
LastTimeUnbuiltLightingWasEncountered = 0x6B8
NextSwitchCountdown = 0x720
NextURL = 0x740
NumStreamingLevelsBeingLoaded = 0x73A
OnBeginPlay = 0x1A0
PauseDelay = 0x6E8
PerModuleDataObjects = 0x78
PlayerNum = 0x678
PreparingLevelNames = 0x750
RealTimeSeconds = 0x6D0
StreamingLevelsPrefix = 0xC8
StreamingVolumeUpdateDelay = 0x67C
TimeSeconds = 0x6C0
UnpausedTimeSeconds = 0x6C8
bActorsInitialized = 0x13C
bAggressiveLOD = 0x13C
bAllowAudioPlayback = 0x13E
bAllowDeferredPhysicsStateCreation = 0x108
bAreConstraintsDirty = 0x13E
bBegunPlay = 0x13D
bDebugPauseExecution = 0x13D
bDoDelayedUpdateCullDistanceVolumes = 0x13C
bDropDetail = 0x13C
bHasEverBeenInitialized = 0x13F
bInTick = 0x13B
bIsBeingCleanedUp = 0x140
bIsBuilt = 0x13B
bIsCameraMoveableWhenPaused = 0x13E
bIsDefaultLevel = 0x13C
bIsLevelStreamingFrozen = 0x13B
bIsRunningConstructionScript = 0x13C
bIsTearingDown = 0x13D
bIsWorldInitialized = 0x13B
bKismetScriptError = 0x13D
bMarkedObjectsPendingKill = 0x768
bMatchStarted = 0x13D
bMaterialParameterCollectionInstanceNeedsDeferredUpdate = 0x13F
bPlayersOnly = 0x13D
bPlayersOnlyPending = 0x13D
bPostTickComponentUpdate = 0x13B
bRequestedBlockOnAsyncLoading = 0x13C
bRequiresHitProxies = 0x13E
bShouldForceUnloadStreamingLevels = 0x13E
bShouldForceVisibleStreamingLevels = 0x13E
bShouldSimulatePhysics = 0x13C
bShouldTick = 0x13E
bStartup = 0x13D
bStreamingDataDirty = 0x13E
bSupportsMakingInvisibleTransactionRequests = 0xDA
bSupportsMakingVisibleTransactionRequests = 0xD8
bTickNewlySpawned = 0x13B
bTriggerPostLoadMap = 0x13B
bWorldWasLoadedThisTick = 0x13B

[UGameViewportClient]
ActiveSplitscreenType = 0x70
AudioDeviceHandle = 0xF8
CurrentBufferVisualizationMode = 0x130
CurrentGroomVisualizationMode = 0x150
CurrentLumenVisualizationMode = 0x140
CurrentNaniteVisualizationMode = 0x138
CurrentSubstrateVisualizationMode = 0x148
CurrentVirtualShadowMapVisualizationMode = 0x158
CursorWidgets = 0x210
DebugProperties = 0x48
EngineShowFlags = 0xB8
GameLayerManagerPtr = 0x120
HardwareCursorCache = 0x170
HardwareCursors = 0x1C0
HighResScreenshotDialog = 0x160
MaxSplitscreenPlayers = 0x68
MouseCaptureMode = 0x3AA
MouseLockMode = 0x3AC
SplitscreenInfo = 0x58
StatHitchesData = 0x3A0
StatUnitData = 0x398
ViewModeIndex = 0xB0
Viewport = 0xE8
ViewportConsole = 0x40
ViewportFrame = 0xF0
ViewportOverlayWidget = 0x110
Window = 0x100
World = 0x78
bDisableSplitScreenOverride = 0x3A8
bDisableWorldRendering = 0x6C
bHasAudioFocus = 0xFC
bHideCursorDuringCapture = 0x3AB
bIgnoreInput = 0x3A9
bIsMouseOverClient = 0x3AD
bIsPlayInEditorViewport = 0x6C
bSuppressTransitionMessage = 0x88
bUseSoftwareCursorWidgets = 0x260

[AGameMode]
EngineMessageClass = 0x348
InactivePlayerArray = 0x350
InactivePlayerStateLifeSpan = 0x360
MatchState = 0x328
MaxInactivePlayers = 0x364
MinRespawnDelay = 0x340
NumBots = 0x33C
NumPlayers = 0x338
NumSpectators = 0x334
NumTravellingPlayers = 0x344
bDelayedStart = 0x330
bHandleDedicatedServerReplays = 0x368

