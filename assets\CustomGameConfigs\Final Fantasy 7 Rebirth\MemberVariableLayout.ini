[FWorldContext]
AudioDeviceID = 0x250
ContextHandle = 0xB0
CustomDescription = 0x258
ExternalReferences = 0x270
GameViewport = 0x210
LastRemoteURL = 0x138
LastURL = 0xD0
LevelsToLoadForPendingMapChange = 0x1B8
PIEAccumulatedTickSeconds = 0x26C
PIEFixedTickSeconds = 0x268
PIEInstance = 0x230
PIEPrefix = 0x238
PendingMapChangeFailureDescription = 0x1D8
RunAsDedicated = 0x24C
ThisCurrentWorld = 0x280
TravelType = 0xC8
TravelURL = 0xB8
bShouldCommitPendingMapChange = 0x1E8
bWaitingOnOnlineSubsystem = 0x24D

[ULocalPlayer]
AspectRatioAxisConstraint = 0x94
CachedUniqueNetId = 0x48
ControllerId = 0xB8
LastViewLocation = 0x88
Origin = 0x78
Size = 0x80
SlateOperations = 0x1A0
ViewportClient = 0x70
bSentSplitJoin = 0xA0

[FByteProperty]
Enum = 0x78

[FSetProperty]
ElementProp = 0x78

[UClass]
ClassAddReferencedObjects = 0xC0
ClassConfigName = 0xE8
ClassConstructor = 0xB0
ClassDefaultObject = 0x118
ClassFlags = 0xCC
ClassGeneratedBy = 0xE0
ClassUnique = 0xC8
ClassVTableHelperCtorCaller = 0xB8
ClassWithin = 0xD8
FirstOwnedClassRep = 0x110
FuncMap = 0x130
Interfaces = 0x1D8
NetFields = 0x100
SparseClassData = 0x120
SparseClassDataStruct = 0x128
SuperFuncMap = 0x180
bCooked = 0xC8

[FMulticastDelegateProperty]
SignatureFunction = 0x78

[FDelegateProperty]
SignatureFunction = 0x78

[UEnum]
CppForm = 0x50
CppType = 0x30
EnumDisplayNameFn = 0x58
EnumFlags = 0x54
Names = 0x40

[UFunction]
EventGraphCallOffset = 0xD0
EventGraphFunction = 0xC8
FirstPropertyToInit = 0xC0
Func = 0xD8
FunctionFlags = 0xB0
NumParms = 0xB4
ParmsSize = 0xB6
RPCId = 0xBA
RPCResponseId = 0xBC
ReturnValueOffset = 0xB8

[UField]
Next = 0x28

[FProperty]
ArrayDim = 0x38
DestructorLinkNext = 0x68
ElementSize = 0x3C
NextRef = 0x60
Offset_Internal = 0x4C
PostConstructLinkNext = 0x70
PropertyFlags = 0x40
PropertyLinkNext = 0x58
RepIndex = 0x48
RepNotifyFunc = 0x50

[FBoolProperty]
ByteMask = 0x7A
ByteOffset = 0x79
FieldMask = 0x7B
FieldSize = 0x78

[FObjectPropertyBase]
PropertyClass = 0x78

[FClassProperty]
MetaClass = 0x80

[AGameModeBase]
DefaultPlayerName = 0x290
GameSession = 0x278
GameSessionClass = 0x230
HUDClass = 0x250
OptionsString = 0x220
PlayerStateClass = 0x248
ServerStatReplicator = 0x288
ServerStatReplicatorClass = 0x270
SpectatorClass = 0x260
bPauseable = 0x2A8
bStartPlayersAsSpectators = 0x2A8
bUseSeamlessTravel = 0x2A8

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[FSoftClassProperty]
MetaClass = 0x80

[FInterfaceProperty]
InterfaceClass = 0x78

[FArrayProperty]
ArrayFlags = 0x80
Inner = 0x78

[FMapProperty]
KeyProp = 0x78
MapFlags = 0xA0
ValueProp = 0x80

[FStructProperty]
Struct = 0x78

[UStruct]
ChildProperties = 0x50
Children = 0x48
DestructorLink = 0x80
MinAlignment = 0x5C
PostConstructLink = 0x88
PropertiesSize = 0x58
PropertyLink = 0x70
RefLink = 0x78
Script = 0x60
ScriptAndPropertyObjectReferences = 0x90
SuperStruct = 0x40
UnresolvedScriptProperties = 0xA0

[FEnumProperty]
Enum = 0x80
UnderlyingProp = 0x78

[FFieldPathProperty]
PropertyClass = 0x78

[FField]
ClassPrivate = 0x8
FlagsPrivate = 0x30
NamePrivate = 0x28
Next = 0x20
Owner = 0x10

[UDataTable]
ImportKeyField = 0x88
RowMap = 0x30
RowStruct = 0x28
bIgnoreExtraFields = 0x80
bIgnoreMissingFields = 0x80
bStripFromClientBuilds = 0x80

[FArchiveState]
ArAllowLazyLoading = 0x2A
ArContainsCode = 0x29
ArContainsMap = 0x29
ArCustomPropertyList = 0x68
ArEngineNetVer = 0x54
ArEngineVer = 0x48
ArForceByteSwapping = 0x29
ArForceUnicode = 0x28
ArGameNetVer = 0x58
ArIgnoreArchetypeRef = 0x29
ArIgnoreClassGeneratedByRef = 0x2A
ArIgnoreClassRef = 0x2A
ArIgnoreOuterRef = 0x2A
ArIsCountingMemory = 0x2A
ArIsCriticalError = 0x29
ArIsError = 0x29
ArIsFilterEditorOnly = 0x2B
ArIsLoading = 0x28
ArIsModifyingWeakAndStrongReferences = 0x2A
ArIsNetArchive = 0x2B
ArIsObjectReferenceCollector = 0x2A
ArIsPersistent = 0x28
ArIsSaveGame = 0x2B
ArIsSaving = 0x28
ArIsTextFormat = 0x28
ArIsTransacting = 0x28
ArLicenseeUE4Ver = 0x44
ArMaxSerializeSize = 0x38
ArNoDelta = 0x29
ArNoIntraPropertyDelta = 0x2A
ArPortFlags = 0x30
ArRequiresLocalizationGather = 0x29
ArSerializingDefaults = 0x2C
ArShouldSkipBulkData = 0x2B
ArUE4Ver = 0x40
ArUseCustomPropertyList = 0x2B
ArUseUnversionedPropertySerialization = 0x28
ArWantBinaryPropertySerialization = 0x28
CookingTargetPlatform = 0x70
CustomVersionContainer = 0x60
NextProxy = 0x90
SerializedProperty = 0x78
bCustomVersionsAreReset = 0x88

[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20

[UScriptStruct]
CppStructOps = 0xB8
StructFlags = 0xB0
bPrepareCppStructOpsCompleted = 0xB4

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[AActor]
ActorHasBegunPlay = 0x5C
AttachmentReplication = 0xA0
AutoReceiveInput = 0xF3
Children = 0x120
ControllingMatineeActors = 0x138
CreationTime = 0x9C
CustomTimeDilation = 0x98
DefaultUpdateOverlapsMethodDuringLevelStreaming = 0x5E
DetachFence = 0x210
InitialLifeSpan = 0x94
InputComponent = 0xF8
InputPriority = 0xF4
LastRenderTime = 0x114
Layers = 0x150
MinNetUpdateFrequency = 0x10C
NetCullDistanceSquared = 0x100
NetDormancy = 0xF1
NetDriverName = 0xE8
NetPriority = 0x110
NetTag = 0x104
NetUpdateFrequency = 0x108
OnActorBeginOverlap = 0x183
OnActorEndOverlap = 0x184
OnActorHit = 0x18D
OnBeginCursorOver = 0x185
OnClicked = 0x187
OnDestroyed = 0x18E
OnEndCursorOver = 0x186
OnEndPlay = 0x18F
OnInputTouchBegin = 0x189
OnInputTouchEnd = 0x18A
OnInputTouchEnter = 0x18B
OnInputTouchLeave = 0x18C
OnReleased = 0x188
OnTakeAnyDamage = 0x180
OnTakePointDamage = 0x181
OnTakeRadialDamage = 0x182
Owner = 0xE0
ParentComponent = 0x160
PrimaryActorTick = 0x28
RemoteRole = 0x5F
ReplicatedMovement = 0x60
Role = 0xF0
RootComponent = 0x130
SpawnCollisionHandlingMethod = 0xF2
Tags = 0x170
TimerHandle_LifeSpanExpired = 0x148
UpdateOverlapsMethodDuringLevelStreaming = 0x5D
bActorBeginningPlayFromLevelStreaming = 0x5B
bActorEnableCollision = 0x5C
bActorInitialized = 0x5B
bActorIsBeingConstructed = 0x168
bActorIsBeingDestroyed = 0x5C
bActorSeamlessTraveled = 0x5A
bActorWantsDestroyDuringBeginPlay = 0x5C
bAllowReceiveTickEventOnDedicatedServer = 0x5B
bAllowTickBeforeBeginPlay = 0x59
bAlwaysRelevant = 0x58
bAutoDestroyWhenFinished = 0x59
bBlockInput = 0x5A
bCanBeDamaged = 0x59
bCanBeInCluster = 0x5B
bCollideWhenPlacing = 0x5A
bEnableAutoLODGeneration = 0x5A
bExchangedRoles = 0x58
bFindCameraComponentWhenViewTarget = 0x5A
bGenerateOverlapEventsDuringLevelStreaming = 0x5A
bHasDeferredComponentRegistration = 0x5C
bHasFinishedSpawning = 0x5B
bHidden = 0x58
bIgnoresOriginShifting = 0x5A
bIsEditorOnlyActor = 0x5A
bNetCheckedInitialPhysicsState = 0x5B
bNetLoadOnClient = 0x59
bNetStartup = 0x58
bNetTemporary = 0x58
bNetUseOwnerRelevancy = 0x59
bOnlyRelevantToOwner = 0x58
bRelevantForLevelBounds = 0x59
bRelevantForNetworkReplays = 0x59
bReplayRewindable = 0x59
bReplicateMovement = 0x58
bReplicates = 0x5B
bRunningUserConstructionScript = 0x5C
bTearOff = 0x58
bTickFunctionsRegistered = 0x5B

[UPlayer]
ConfiguredInternetSpeed = 0x3C
ConfiguredLanSpeed = 0x40
CurrentNetSpeed = 0x38

[UWorld]
ActiveLevelCollectionIndex = 0x158
AudioTimeSeconds = 0x5A4
AuthorityGameMode = 0x118
BuildStreamingDataTimer = 0x3C8
CleanupWorldTag = 0x614
CommittedPersistentLevelName = 0x608
DeltaTimeSeconds = 0x5A8
ExtraReferencedObjects = 0x68
LastTimeUnbuiltLightingWasEncountered = 0x590
NextSwitchCountdown = 0x5D4
NextURL = 0x5E8
NumStreamingLevelsBeingLoaded = 0x5E2
OriginOffsetThisFrame = 0x5C8
PauseDelay = 0x5AC
PerModuleDataObjects = 0x78
PlayerNum = 0x550
PreparingLevelNames = 0x5F8
RealTimeSeconds = 0x5A0
StreamingLevelsPrefix = 0xC0
StreamingVolumeUpdateDelay = 0x554
TimeSeconds = 0x598
URL = 0x478
UnpausedTimeSeconds = 0x59C
ViewLocationsRenderedLastFrame = 0xF8
bActorsInitialized = 0x10C
bAggressiveLOD = 0x10C
bAllowAudioPlayback = 0x10E
bAreConstraintsDirty = 0x10E
bBegunPlay = 0x10D
bDebugPauseExecution = 0x10D
bDoDelayedUpdateCullDistanceVolumes = 0x10C
bDropDetail = 0x10C
bInTick = 0x10B
bIsBuilt = 0x10B
bIsCameraMoveableWhenPaused = 0x10E
bIsDefaultLevel = 0x10C
bIsLevelStreamingFrozen = 0x10B
bIsRunningConstructionScript = 0x10C
bIsTearingDown = 0x10D
bIsWorldInitialized = 0x10B
bKismetScriptError = 0x10D
bMarkedObjectsPendingKill = 0x610
bMatchStarted = 0x10D
bMaterialParameterCollectionInstanceNeedsDeferredUpdate = 0x10F
bPlayersOnly = 0x10D
bPlayersOnlyPending = 0x10D
bPostTickComponentUpdate = 0x10B
bRequestedBlockOnAsyncLoading = 0x10C
bRequiresHitProxies = 0x10E
bShouldForceUnloadStreamingLevels = 0x10E
bShouldForceVisibleStreamingLevels = 0x10E
bShouldSimulatePhysics = 0x10C
bShouldTick = 0x10E
bStartup = 0x10D
bStreamingDataDirty = 0x10E
bTickNewlySpawned = 0x10B
bTriggerPostLoadMap = 0x10B
bWorldWasLoadedThisTick = 0x10B

[UGameViewportClient]
ActiveSplitscreenType = 0x70
AudioDeviceHandle = 0xF0
CurrentBufferVisualizationMode = 0x128
CursorWidgets = 0x1E0
DebugProperties = 0x48
EngineShowFlags = 0xB8
GameLayerManagerPtr = 0x118
HardwareCursorCache = 0x140
HardwareCursors = 0x190
HighResScreenshotDialog = 0x130
MaxSplitscreenPlayers = 0x68
MouseCaptureMode = 0x35A
MouseLockMode = 0x35C
SplitscreenInfo = 0x58
StatHitchesData = 0x350
StatUnitData = 0x348
ViewModeIndex = 0xB0
Viewport = 0xE0
ViewportConsole = 0x40
ViewportFrame = 0xE8
ViewportOverlayWidget = 0x108
Window = 0xF8
World = 0x78
bDisableSplitScreenOverride = 0x358
bDisableWorldRendering = 0x6C
bHasAudioFocus = 0xF4
bHideCursorDuringCapture = 0x35B
bIgnoreInput = 0x359
bIsMouseOverClient = 0x35D
bIsPlayInEditorViewport = 0x6C
bShowTitleSafeZone = 0x6C
bSuppressTransitionMessage = 0x88
bUseSoftwareCursorWidgets = 0x230

[AGameMode]
EngineMessageClass = 0x2E0
InactivePlayerArray = 0x2E8
InactivePlayerStateLifeSpan = 0x2F8
MatchState = 0x2C0
MaxInactivePlayers = 0x2FC
MinRespawnDelay = 0x2D8
NumBots = 0x2D4
NumPlayers = 0x2D0
NumSpectators = 0x2CC
NumTravellingPlayers = 0x2DC
bDelayedStart = 0x2C8
bHandleDedicatedServerReplays = 0x300

[FUObjectHashTables]
ClassToChildListMap = 0x168
ClassToChildListMapVersion = 0x1B8
ClassToObjectListMap = 0x118
Hash = 0x28
HashOuter = 0x78
ObjectOuterMap = 0xC8
ObjectToPackageMap = 0x210
PackageToObjectListMap = 0x1C0

