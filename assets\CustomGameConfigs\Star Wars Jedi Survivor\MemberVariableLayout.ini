[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[FField]
ClassPrivate = 0x8
FlagsPrivate = 0x30
NamePrivate = 0x28
Next = 0x20
Owner = 0x10

[FSoftClassProperty]
MetaClass = 0x80

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[FEnumProperty]
Enum = 0x80
UnderlyingProp = 0x78

[UStruct]
ChildProperties = 0x50
Children = 0x48
DestructorLink = 0x80
MinAlignment = 0x5C
PostConstructLink = 0x88
PropertiesSize = 0x58
PropertyLink = 0x70
RefLink = 0x78
Script = 0x60
ScriptAndPropertyObjectReferences = 0x90
SuperStruct = 0x40
UnresolvedScriptProperties = 0xA0

[UFunction]
EventGraphCallOffset = 0xD0
EventGraphFunction = 0xC8
FirstPropertyToInit = 0xC0
Func = 0xD8
FunctionFlags = 0xB0
NumParms = 0xB4
ParmsSize = 0xB6
RPCId = 0xBA
RPCResponseId = 0xBC
ReturnValueOffset = 0xB8

[UField]
Next = 0x28

[FDelegateProperty]
SignatureFunction = 0x78

[FProperty]
ArrayDim = 0x38
DestructorLinkNext = 0x68
ElementSize = 0x3C
NextRef = 0x60
Offset_Internal = 0x4C
PostConstructLinkNext = 0x70
PropertyFlags = 0x40
PropertyLinkNext = 0x58
RepIndex = 0x48
RepNotifyFunc = 0x50

[FMulticastDelegateProperty]
SignatureFunction = 0x78

[FObjectPropertyBase]
PropertyClass = 0x78

[UScriptStruct]
CppStructOps = 0xB8
StructFlags = 0xB0
bPrepareCppStructOpsCompleted = 0xB4

[UWorld]
ActiveLevelCollectionIndex = 0x158
AudioTimeSeconds = 0x5A4
BuildStreamingDataTimer = 0x3C8
CleanupWorldTag = 0x614
CommittedPersistentLevelName = 0x608
DeltaTimeSeconds = 0x5A8
ExtraReferencedObjects = 0x68
LastTimeUnbuiltLightingWasEncountered = 0x590
NextSwitchCountdown = 0x5D4
NextURL = 0x5E8
NumStreamingLevelsBeingLoaded = 0x5E2
OriginOffsetThisFrame = 0x5C8
PauseDelay = 0x5AC
PerModuleDataObjects = 0x78
PlayerNum = 0x550
PreparingLevelNames = 0x5F8
RealTimeSeconds = 0x5A0
StreamingLevelsPrefix = 0xC0
StreamingVolumeUpdateDelay = 0x554
TimeSeconds = 0x598
UnpausedTimeSeconds = 0x59C
ViewLocationsRenderedLastFrame = 0xF8
bActorsInitialized = 0x10C
bAggressiveLOD = 0x10C
bAllowAudioPlayback = 0x10E
bAreConstraintsDirty = 0x10E
bBegunPlay = 0x10D
bDebugPauseExecution = 0x10D
bDoDelayedUpdateCullDistanceVolumes = 0x10C
bDropDetail = 0x10C
bInTick = 0x10B
bIsBuilt = 0x10B
bIsCameraMoveableWhenPaused = 0x10E
bIsDefaultLevel = 0x10C
bIsLevelStreamingFrozen = 0x10B
bIsRunningConstructionScript = 0x10C
bIsTearingDown = 0x10D
bIsWorldInitialized = 0x10B
bKismetScriptError = 0x10D
bMarkedObjectsPendingKill = 0x610
bMatchStarted = 0x10D
bMaterialParameterCollectionInstanceNeedsDeferredUpdate = 0x10F
bPlayersOnly = 0x10D
bPlayersOnlyPending = 0x10D
bPostTickComponentUpdate = 0x10B
bRequestedBlockOnAsyncLoading = 0x10C
bRequiresHitProxies = 0x10E
bShouldForceUnloadStreamingLevels = 0x10E
bShouldForceVisibleStreamingLevels = 0x10E
bShouldSimulatePhysics = 0x10C
bShouldTick = 0x10E
bStartup = 0x10D
bStreamingDataDirty = 0x10E
bTickNewlySpawned = 0x10B
bTriggerPostLoadMap = 0x10B
bWorldWasLoadedThisTick = 0x10B

[UClass]
ClassAddReferencedObjects = 0xC0
ClassConfigName = 0xF0
ClassConstructor = 0xB0
ClassDefaultObject = 0x120
ClassFlags = 0xD0
ClassGeneratedBy = 0xE8
ClassUnique = 0xC8
ClassVTableHelperCtorCaller = 0xB8
ClassWithin = 0xE0
FirstOwnedClassRep = 0x118
Interfaces = 0x1E0
NetFields = 0x108
SparseClassData = 0x128
SparseClassDataStruct = 0x130
bCooked = 0xC8

[FSetProperty]
ElementProp = 0x78

[UEnum]
CppForm = 0x50
CppType = 0x30
EnumDisplayNameFn = 0x58
EnumFlags = 0x54
Names = 0x40

[FStructProperty]
Struct = 0x78

[FArrayProperty]
Inner = 0x78

[FMapProperty]
KeyProp = 0x78
ValueProp = 0x80

[FBoolProperty]
ByteMask = 0x7A
ByteOffset = 0x79
FieldMask = 0x7B
FieldSize = 0x78

[FByteProperty]
Enum = 0x78

[FClassProperty]
MetaClass = 0x80

[FInterfaceProperty]
InterfaceClass = 0x78

[FFieldPathProperty]
PropertyClass = 0x78

