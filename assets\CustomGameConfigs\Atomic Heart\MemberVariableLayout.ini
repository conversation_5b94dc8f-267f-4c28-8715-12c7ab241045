[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[FField]
ClassPrivate = 0x8
FlagsPrivate = 0x30
NamePrivate = 0x28
Next = 0x20
Owner = 0x10

[FSoftClassProperty]
MetaClass = 0x80

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[FEnumProperty]
Enum = 0x80
UnderlyingProp = 0x78

[UStruct]
ChildProperties = 0x58
Children = 0x50
DestructorLink = 0x88
MinAlignment = 0x64
PostConstructLink = 0x90
PropertiesSize = 0x60
PropertyLink = 0x78
RefLink = 0x80
Script = 0x68
ScriptAndPropertyObjectReferences = 0x98
SuperStruct = 0x48
UnresolvedScriptProperties = 0xA8

[UFunction]
EventGraphCallOffset = 0xD8
EventGraphFunction = 0xD0
FirstPropertyToInit = 0xC8
Func = 0xE0
FunctionFlags = 0xB8
NumParms = 0xBC
ParmsSize = 0xBE
RPCId = 0xC2
RPCResponseId = 0xC4
ReturnValueOffset = 0xC0

[UField]
Next = 0x30

[FDelegateProperty]
SignatureFunction = 0x78

[FProperty]
ArrayDim = 0x38
DestructorLinkNext = 0x68
ElementSize = 0x3C
NextRef = 0x60
Offset_Internal = 0x4C
PostConstructLinkNext = 0x70
PropertyFlags = 0x40
PropertyLinkNext = 0x58
RepIndex = 0x48
RepNotifyFunc = 0x50

[FMulticastDelegateProperty]
SignatureFunction = 0x78

[FObjectPropertyBase]
PropertyClass = 0x78

[UScriptStruct]
CppStructOps = 0xC0
StructFlags = 0xB8
bPrepareCppStructOpsCompleted = 0xBC

; Not updated.
; To update: Add 8 bytes to each offset.
[UWorld]
ActiveLevelCollectionIndex = 0x158
AudioTimeSeconds = 0x5AC
BuildStreamingDataTimer = 0x3D0
CleanupWorldTag = 0x61C
CommittedPersistentLevelName = 0x610
DeltaTimeSeconds = 0x5B0
ExtraReferencedObjects = 0x68
LastTimeUnbuiltLightingWasEncountered = 0x598
NextSwitchCountdown = 0x5DC
NextURL = 0x5F0
NumStreamingLevelsBeingLoaded = 0x5EA
OriginOffsetThisFrame = 0x5D0
PauseDelay = 0x5B4
PerModuleDataObjects = 0x78
PlayerNum = 0x558
PreparingLevelNames = 0x600
RealTimeSeconds = 0x5A8
StreamingLevelsPrefix = 0xC0
StreamingVolumeUpdateDelay = 0x55C
TimeSeconds = 0x5A0
UnpausedTimeSeconds = 0x5A4
ViewLocationsRenderedLastFrame = 0xF8
bActorsInitialized = 0x10C
bAggressiveLOD = 0x10C
bAllowAudioPlayback = 0x10E
bAreConstraintsDirty = 0x10E
bBegunPlay = 0x10D
bDebugPauseExecution = 0x10D
bDoDelayedUpdateCullDistanceVolumes = 0x10C
bDropDetail = 0x10C
bInTick = 0x10B
bIsBuilt = 0x10B
bIsCameraMoveableWhenPaused = 0x10E
bIsDefaultLevel = 0x10C
bIsLevelStreamingFrozen = 0x10B
bIsRunningConstructionScript = 0x10C
bIsTearingDown = 0x10D
bIsWorldInitialized = 0x10B
bKismetScriptError = 0x10D
bMarkedObjectsPendingKill = 0x618
bMatchStarted = 0x10D
bMaterialParameterCollectionInstanceNeedsDeferredUpdate = 0x10F
bPlayersOnly = 0x10D
bPlayersOnlyPending = 0x10D
bPostTickComponentUpdate = 0x10B
bRequestedBlockOnAsyncLoading = 0x10C
bRequiresHitProxies = 0x10E
bShouldForceUnloadStreamingLevels = 0x10E
bShouldForceVisibleStreamingLevels = 0x10E
bShouldSimulatePhysics = 0x10C
bShouldTick = 0x10E
bStartup = 0x10D
bStreamingDataDirty = 0x10E
bTickNewlySpawned = 0x10B
bTriggerPostLoadMap = 0x10B
bWorldWasLoadedThisTick = 0x10B

[UClass]
ClassAddReferencedObjects = 0xC8
ClassConfigName = 0xF0
ClassConstructor = 0xB8
ClassDefaultObject = 0x120
ClassFlags = 0xD4
ClassGeneratedBy = 0xE8
ClassUnique = 0xD0
ClassVTableHelperCtorCaller = 0xC0
ClassWithin = 0xE0
FirstOwnedClassRep = 0x118
Interfaces = 0x1E0
NetFields = 0x108
SparseClassData = 0x128
SparseClassDataStruct = 0x130
bCooked = 0xD0

[FSetProperty]
ElementProp = 0x78

[UEnum]
CppForm = 0x58
CppType = 0x38
EnumDisplayNameFn = 0x60
EnumFlags = 0x5C
Names = 0x48

[FStructProperty]
Struct = 0x78

[FArrayProperty]
Inner = 0x78

[FMapProperty]
KeyProp = 0x78
ValueProp = 0x80

[FBoolProperty]
ByteMask = 0x7A
ByteOffset = 0x79
FieldMask = 0x7B
FieldSize = 0x78

[FByteProperty]
Enum = 0x78

[FClassProperty]
MetaClass = 0x80

[FInterfaceProperty]
InterfaceClass = 0x78

[FFieldPathProperty]
PropertyClass = 0x78

