[ITextData]
__vecDelDtor
GetSourceString
GetDisplayString
GetLocalizedString
GetGlobalHistoryRevision
GetLocalHistoryRevision
GetTextHistory
GetMutableTextHistory

[FArchive]
__vecDelDtor
; FArchive* operator<<(FWeakObjectPtr*)const;
operator<<
; FArchive* operator<<_1(FSoftObjectPath*)const;
operator<<_1
; FArchive* operator<<_2(FSoftObjectPtr*)const;
operator<<_2
; FArchive* operator<<_3(FObjectPtr*)const;
operator<<_3
; FArchive* operator<<_4(FLazyObjectPtr*)const;
operator<<_4
; FArchive* operator<<_5(FField**)const;
operator<<_5
; FArchive* operator<<_6(UObject**)const;
operator<<_6
; FArchive* operator<<_7(FText*)const;
operator<<_7
; FArchive* operator<<_8(FName*)const;
operator<<_8
ForceBlueprintFinalization
Serialize
SerializeBits
SerializeInt
SerializeIntPacked
SerializeIntPacked64
Preload
Seek
; void AttachBulkData(UE::Serialization::FEditorBulkData*)const;
AttachBulkData
; void AttachBulkData_1(UObject*, FBulkData*)const;
AttachBulkData_1
; void DetachBulkData(UE::Serialization::FEditorBulkData*, bool)const;
DetachBulkData
; void DetachBulkData_1(FBulkData*, bool)const;
DetachBulkData_1
SerializeBulkData
IsProxyOf
Precache
FlushCache
SetCompressionMap
Flush
Close
MarkScriptSerializationStart
MarkScriptSerializationEnd
MarkSearchableName
UsingCustomVersion
GetCacheableArchive
PushSerializedProperty
PopSerializedProperty
AttachExternalReadDependency
PushFileRegionType
PopFileRegionType

[FMalloc]
__vecDelDtor
Exec_Runtime
Exec_Dev
Exec_Editor
Malloc
TryMalloc
Realloc
TryRealloc
Free
QuantizeSize
GetAllocationSize
Trim
SetupTLSCachesOnCurrentThread
ClearAndDisableTLSCachesOnCurrentThread
InitializeStatsMetadata
UpdateStats
GetAllocatorStats
DumpAllocatorStats
IsInternallyThreadSafe
ValidateHeap
GetDescriptiveName
OnMallocInitialized
OnPreFork
OnPostFork

[UObject]
__vecDelDtor
GetDetailedInfoInternal
PostInitProperties
PostReinitProperties
PostCDOContruct
; void PreSaveRoot(FObjectPreSaveRootContext)const;
PreSaveRoot
; bool PreSaveRoot_1(wchar_t*)const;
PreSaveRoot_1
; void PostSaveRoot(FObjectPostSaveRootContext)const;
PostSaveRoot
; void PostSaveRoot_1(bool)const;
PostSaveRoot_1
; void PreSave(FObjectPreSaveContext)const;
PreSave
; void PreSave_1(ITargetPlatform*)const;
PreSave_1
ResolveSubobject
IsReadyForAsyncPostLoad
PostLoad
PostLoadSubobjects
BeginDestroy
IsReadyForFinishDestroy
FinishDestroy
; void Serialize(FStructuredArchiveRecord)const;
Serialize
; void Serialize_1(FArchive*)const;
Serialize_1
ShutdownAfterError
PostInterpChange
PostRename
PreDuplicate
; void PostDuplicate(EDuplicateMode::Type)const;
PostDuplicate
; void PostDuplicate_1(bool)const;
PostDuplicate_1
NeedsLoadForClient
NeedsLoadForServer
NeedsLoadForTargetPlatform
NeedsLoadForEditorGame
IsEditorOnly
HasNonEditorOnlyReferences
IsPostLoadThreadSafe
IsDestructionThreadSafe
GetPreloadDependencies
GetPrestreamPackages
ExportCustomProperties
ImportCustomProperties
PostEditImport
PostReloadConfig
Rename
GetDesc
GetWorld
GetNativePropertyValues
GetResourceSizeEx
GetExporterName
GetRestoreForUObjectOverwrite
AreNativePropertiesIdenticalTo
; void GetAssetRegistryTags(TArray<UObject::FAssetRegistryTag,TSizedDefaultAllocator<32> >*)const;
GetAssetRegistryTags
; void GetAssetRegistryTags_1(FAssetRegistryTagsContext)const;
GetAssetRegistryTags_1
IsAsset
GetPrimaryAssetId
IsLocalizedResource
IsSafeForRootSet
TagSubobjects
GetLifetimeReplicatedProps
GetReplicatedCustomConditionState
RegisterReplicationFragments
IsNameStableForNetworking
IsFullNameStableForNetworking
IsSupportedForNetworking
GetSubobjectsWithStableNamesForNetworking
PreNetReceive
PostNetReceive
PostRepNotifies
PreDestroyFromReplication
BuildSubobjectMapping
GetConfigOverridePlatform
OverrideConfigSection
OverridePerObjectConfigSection
ProcessEvent
GetFunctionCallspace
CallRemoteFunction
ProcessConsoleExec
RegenerateClass
MarkAsEditorOnlySubobject
CheckDefaultSubobjectsInternal
ValidateGeneratedRepEnums
SetNetPushIdDynamic
GetNetPushIdDynamic

[UObjectBase]
__vecDelDtor
RegisterDependencies
DeferredRegister
GetFNameForStatID

[UObjectBaseUtility]
__vecDelDtor
CanBeClusterRoot
CanBeInCluster
CreateCluster
OnClusterMarkedAsPendingKill

[UDataTable]
__vecDelDtor
GetNonConstRowMap
AddRowInternal
RemoveRowInternal
; TMap<FName, unsigned char *>* GetRowMap()const;
GetRowMap
; TMap<FName, unsigned char *>* GetRowMap_1()const;
GetRowMap_1
AllowDuplicateRowsOnImport
EmptyTable
RemoveRow
AddRow

[FArchiveState]
__vecDelDtor
GetInnermostState
CountBytes
GetArchiveName
GetLinker
Tell
TotalSize
AtEnd
GetArchetypeFromLoader
EngineNetVer
GameNetVer
GetCustomVersions
SetCustomVersions
ResetCustomVersions
SetFilterEditorOnly
UseToResolveEnumerators
ShouldSkipProperty
SetSerializedProperty
SetSerializedPropertyChain
SetSerializeContext
GetSerializeContext
Reset
SetIsLoading
SetIsLoadingFromCookedPackage
SetIsSaving
SetIsTransacting
SetIsTextFormat
SetWantBinaryPropertySerialization
SetUseUnversionedPropertySerialization
SetForceUnicode
SetIsPersistent
SetUEVer
SetLicenseeUEVer
SetEngineVer
SetEngineNetVer
SetGameNetVer

[AGameModeBase]
__vecDelDtor
InitializeHUDForPlayer_Implementation
InitStartSpot_Implementation
SpawnDefaultPawnAtTransform_Implementation
SpawnDefaultPawnFor_Implementation
PlayerCanRestart_Implementation
FindPlayerStart_Implementation
ChoosePlayerStart_Implementation
CanSpectate_Implementation
MustSpectate_Implementation
HandleStartingNewPlayer_Implementation
ShouldReset_Implementation
GetDefaultPawnClassForController_Implementation
InitGame
InitGameState
GetGameSessionClass
GetNumPlayers
GetNumSpectators
StartPlay
HasMatchStarted
HasMatchEnded
SetPause
ClearPause
AllowPausing
IsPaused
ResetLevel
ReturnToMainMenuHost
CanServerTravel
ProcessServerTravel
GetSeamlessTravelActorList
SwapPlayerControllers
GetPlayerControllerClassToSpawnForSeamlessTravel
HandleSeamlessTravelPlayer
PostSeamlessTravel
StartToLeaveMap
GameWelcomePlayer
PreLogin
PreLoginAsync
Login
PostLogin
OnPostLogin
Logout
; APlayerController* SpawnPlayerController(ENetRole, UE::Math::TVector<double>*, UE::Math::TRotator<double>*)const;
SpawnPlayerController
; APlayerController* SpawnPlayerController_1(ENetRole, FString*)const;
SpawnPlayerController_1
SpawnReplayPlayerController
ChangeName
RestartPlayer
RestartPlayerAtPlayerStart
RestartPlayerAtTransform
SetPlayerDefaults
AllowCheats
IsHandlingReplays
SpawnPlayerFromSimulate
UpdatePlayerStartSpot
ShouldStartInCinematicMode
UpdateGameplayMuteList
InitNewPlayer
GenericPlayerInitialization
ReplicateStreamingStatus
ShouldSpawnAtStartSpot
FinishRestartPlayer
FailedToRestartPlayer
ProcessClientTravel
InitSeamlessTravelPlayer
SpawnPlayerControllerCommon

[FOutputDevice]
__vecDelDtor
; void Serialize(wchar_t*, ELogVerbosity::Type, FName*, double)const;
Serialize
; void Serialize_1(wchar_t*, ELogVerbosity::Type, FName*)const;
Serialize_1
SerializeRecord
Flush
TearDown
Dump
IsMemoryOnly
CanBeUsedOnAnyThread
CanBeUsedOnMultipleThreads
CanBeUsedOnPanicThread

[FMulticastDelegateProperty]
__vecDelDtor
GetMulticastDelegate
SetMulticastDelegate
AddDelegate
RemoveDelegate
ClearDelegate
GetMulticastScriptDelegate

[FNumericProperty]
__vecDelDtor
IsFloatingPoint
IsInteger
GetIntPropertyEnum
; void SetIntPropertyValue(void*, int64)const;
SetIntPropertyValue
; void SetIntPropertyValue_1(void*, uint64)const;
SetIntPropertyValue_1
SetFloatingPointPropertyValue
SetNumericPropertyValueFromString
SetNumericPropertyValueFromString_InContainer
GetSignedIntPropertyValue
GetSignedIntPropertyValue_InContainer
GetUnsignedIntPropertyValue
GetUnsignedIntPropertyValue_InContainer
GetFloatingPointPropertyValue
GetNumericPropertyValueToString
GetNumericPropertyValueToString_InContainer
CanHoldDoubleValueInternal
CanHoldSignedValueInternal
CanHoldUnsignedValueInternal

[UField]
__vecDelDtor
AddCppProperty
Bind

[FField]
__vecDelDtor
Serialize
PostLoad
GetPreloadDependencies
BeginDestroy
AddReferencedObjects
AddCppProperty
Bind
PostDuplicate
GetInnerFieldByName
GetInnerFields

[FProperty]
__vecDelDtor
GetCPPMacroType
PassCPPArgsByRef
GetCPPType
GetCPPTypeForwardDeclaration
HasSetter
HasGetter
HasSetterOrGetter
CallSetter
CallGetter
LinkInternal
ConvertFromType
Identical
SerializeItem
NetSerializeItem
SupportsNetSharedSerialization
ExportTextItem
GetValueAddressAtIndex_Direct
ExportText_Internal
ImportText_Internal
CopyValuesInternal
GetValueTypeHashInternal
CopySingleValueToScriptVM
CopyCompleteValueToScriptVM
CopyCompleteValueToScriptVM_InContainer
CopyCompleteValueFromScriptVM_InContainer
CopySingleValueFromScriptVM
CopyCompleteValueFromScriptVM
ClearValueInternal
DestroyValueInternal
InitializeValueInternal
GetID
InstanceSubobjects
GetMinAlignment
ContainsObjectReference
EmitReferenceInfo
UseBinaryOrNativeSerialization
LoadTypeName
SaveTypeName
CanSerializeFromTypeName
SameType

[FObjectPropertyBase]
__vecDelDtor
GetCPPTypeCustom
LoadObjectPropertyValue
GetObjectPtrPropertyValue
GetObjectPropertyValue
GetObjectPropertyValue_InContainer
SetObjectPropertyValue
SetObjectPtrPropertyValue
SetObjectPropertyValue_InContainer
CheckValidObject
AllowObjectTypeReinterpretationTo
AllowCrossLevel

[UStruct]
__vecDelDtor
GetInheritanceSuper
Link
; void SerializeBin(FStructuredArchiveSlot, void*)const;
SerializeBin
; void SerializeBin_1(FArchive*, void*)const;
SerializeBin_1
; void SerializeTaggedProperties(FStructuredArchiveSlot, uint8*, UStruct*, uint8*, UObject*)const;
SerializeTaggedProperties
; void SerializeTaggedProperties_1(FArchive*, uint8*, UStruct*, uint8*, UObject*)const;
SerializeTaggedProperties_1
PreloadChildren
InitializeStruct
DestroyStruct
CustomFindProperty
SerializeExpr
GetPrefixCPP
GetContainerSize
SetSuperStruct
; FString GetAuthoredNameForField(FField*)const;
GetAuthoredNameForField
; FString GetAuthoredNameForField_1(UField*)const;
GetAuthoredNameForField_1
IsStructTrashed
FindPropertyNameFromGuid
FindPropertyGuidFromName
ArePropertyGuidsAvailable

[UScriptStruct::ICppStructOps]
__vecDelDtor
GetCapabilities
Construct
ConstructForTests
Destruct
; bool Serialize(FStructuredArchiveSlot, void*)const;
Serialize
; bool Serialize_1(FArchive*, void*)const;
Serialize_1
PostSerialize
NetSerialize
NetDeltaSerialize
PostScriptConstruct
GetPreloadDependencies
Copy
Identical
ExportTextItem
ImportTextItem
FindInnerPropertyInstance
AddStructReferencedObjects
SerializeFromMismatchedTag
StructuredSerializeFromMismatchedTag
GetStructTypeHash
GetCppTraits

[AActor]
__vecDelDtor
OnRep_ReplicateMovement
TearOff
HasNetOwner
HasLocalNetOwner
OnRep_Owner
SetReplicateMovement
OnRep_AttachmentReplication
IsReplicationPausedForConnection
OnReplicationPausedChanged
ReplicateSubobjects
OnSubobjectCreatedFromReplication
OnSubobjectDestroyFromReplication
PreReplication
PreReplicationForReplay
RewindForReplay
OnRep_Instigator
EnableInput
CreateInputComponent
DisableInput
GetActorBounds
GetVelocity
SetActorHiddenInGame
K2_DestroyActor
AddTickPrerequisiteActor
AddTickPrerequisiteComponent
RemoveTickPrerequisiteActor
RemoveTickPrerequisiteComponent
BeginPlay
EndPlay
NotifyActorBeginOverlap
NotifyActorEndOverlap
NotifyActorBeginCursorOver
NotifyActorEndCursorOver
NotifyActorOnClicked
NotifyActorOnReleased
NotifyActorOnInputTouchBegin
**************************
NotifyActorOnInputTouchEnter
NotifyActorOnInputTouchLeave
NotifyHit
SetLifeSpan
GetLifeSpan
IsRuntimeOnly
GatherCurrentMovement
GetDefaultAttachComponent
ApplyWorldOffset
IsLevelBoundsRelevant
IsHLODRelevant
HasHLODRelevantComponents
GetHLODRelevantComponents
GetNetPriority
GetReplayPriority
GetNetDormancy
OnActorChannelOpen
UseShortConnectTimeout
OnSerializeNewActor
OnNetCleanup
SetActorTickEnabled
TickActor
AsyncPhysicsTickActor
PostActorCreated
LifeSpanExpired
PostNetReceiveRole
PostNetInit
OnRep_ReplicatedMovement
PostNetReceiveLocationAndRotation
PostNetReceiveVelocity
PostNetReceivePhysicState
SetOwner
CheckStillInWorld
GetPhysicsVolume
Tick
ShouldTickIfViewportsOnly
IsNetRelevantFor
IsReplayRelevantFor
IsRelevancyOwnerFor
PreInitializeComponents
PostInitializeComponents
DispatchPhysicsCollisionHit
GetNetOwner
GetNetOwningPlayer
GetNetConnection
DestroyNetworkActorHandled
IsSelectionParentOfAttachedActors
IsSelectionChild
GetSelectionParent
GetRootSelectionParent
SupportsSubRootSelection
PushSelectionToProxies
RegisterAllComponents
PreRegisterAllComponents
PostRegisterAllComponents
UnregisterAllComponents
PostUnregisterAllComponents
ReregisterAllComponents
MarkComponentsAsPendingKill
MarkComponentsAsGarbage
InvalidateLightingCacheDetailed
TeleportTo
TeleportSucceeded
ClearCrossLevelReferences
IsBasedOnActor
IsAttachedTo
OnConstruction
; void BeginReplication()const;
BeginReplication
EndReplication
RegisterActorTickFunctions
Destroyed
FellOutOfWorld
OutsideWorldBounds
GetComponentsBoundingBox
CalculateComponentsBoundingBoxInLocalSpace
GetComponentsBoundingCylinder
GetSimpleCollisionCylinder
IsRootComponentCollisionRegistered
TornOff
GetComponentsCollisionResponseToChannel
CanBeBaseForCharacter
TakeDamage
InternalTakeRadialDamage
InternalTakePointDamage
BecomeViewTarget
EndViewTarget
CalcCamera
HasActiveCameraComponent
HasActivePawnControlCameraComponent
GetHumanReadableName
Reset
GetLastRenderTime
ForceNetRelevant
ForceNetUpdate
PrestreamTextures
GetActorEyesViewPoint
GetTargetLocation
PostRenderFor
FindComponentByClass
FindComponentByInterface
AllowActorComponentToReplicate
IsComponentRelevantForNavigation
DisplayDebug

[ULocalPlayer]
__vecDelDtor
GetViewPoint
; TSharedPtr<FSlateUser const ,1> GetSlateUser()const;
GetSlateUser
; TSharedPtr<FSlateUser,1> GetSlateUser_1()const;
GetSlateUser_1
CalcSceneViewInitOptions
CalcSceneView
; void PlayerAdded(UGameViewportClient*, FPlatformUserId)const;
PlayerAdded
; void PlayerAdded_1(UGameViewportClient*, int32)const;
PlayerAdded_1
InitOnlineSession
PlayerRemoved
SpawnPlayActor
PreBeginHandshake
SendSplitJoin
SetControllerId
SetPlatformUserId
GetPlatformUserIndex
GetLocalPlayerIndex
GetNickname
GetGameLoginOptions
GetUniqueNetIdForPlatformUser
GetPreferredUniqueNetId
GetProjectionData
CleanupViewState

[UEngine]
__vecDelDtor
WorldAdded
WorldDestroyed
IsInitialized
GetDefaultWorldFeatureLevel
Init
Start
PreExit
ReleaseAudioDeviceManager
Tick
UpdateTimeAndHandleMaxTickRate
CorrectNegativeTimeDelta
GetMaxTickRate
GetMaxFPS
SetMaxFPS
UpdateRunningAverageDeltaTime
IsAllowedFramerateSmoothing
IsRenderingSuspended
OnLostFocusPause
ShouldThrottleCPUUsage
IsControllerIdUsingPlatformUserId
ShouldDrawBrushWireframe
GetMapBuildCancelled
SetMapBuildCancelled
AllowSelectTranslucent
OnlyLoadEditorVisibleLevelsInPIE
PreferToStreamLevelsInPIE
GetSpriteCategoryIndex
; float GetTimeBetweenGarbageCollectionPasses(bool)const;
GetTimeBetweenGarbageCollectionPasses
StartFPSChart
StopFPSChart
ProcessToggleFreezeCommand
ProcessToggleFreezeStreamingCommand
ShouldForceGarbageCollection
IsSplitScreen
HasMultipleLocalPlayers
IsSettingUpPlayWorld
GetGameViewportWidget
FocusNextPIEWorld
ResetPIEAudioSetting
GetNextPIEViewport
RemapGamepadControllerIdForPIE
NotifyToolsOfObjectReplacement
UseSound
CreatePIEWorldByDuplication
PostCreatePIEWorld
Experimental_ShouldPreDuplicateMap
InitializeAudioDeviceManager
InitializeHMDDevice
InitializeEyeTrackingDevice
RecordHMDAnalytics
InitializeObjectReferences
InitializePortalServices
InitializeRunningAverageDeltaTime
SpawnServerActors
HandleNetworkFailure
HandleTravelFailure
HandleNetworkLagStateChanged
; bool NetworkRemapPath(UPendingNetGame*, FString*, bool)const;
NetworkRemapPath
; bool NetworkRemapPath_1(UNetConnection*, FString*, bool)const;
NetworkRemapPath_1
HandleOpenCommand
HandleTravelCommand
HandleStreamMapCommand
HandleServerTravelCommand
HandleDisconnectCommand
HandleReconnectCommand
Browse
TickWorldTravel
LoadMap
RedrawViewports
TriggerStreamingDataRebuild
LoadMapRedrawViewports
CancelAllPending
; void CancelPending(UNetDriver*)const;
CancelPending
; void CancelPending_1(FWorldContext*)const;
CancelPending_1
; void CancelPending_2(UWorld*, UPendingNetGame*)const;
CancelPending_2
WorldIsPIEInNewViewport
CheckAndHandleStaleWorldObjectReferences
DestroyWorldContext
AreEditorAnalyticsEnabled
CreateStartupAnalyticsAttributes
IsAutosaving
ShouldDoAsyncEndOfFrameTasks
MovePendingLevel
ShouldShutdownWorldNetDriver
HandleBrowseToDefaultMapFailure
HandleNetworkFailure_NotifyGameInstance
HandleTravelFailure_NotifyGameInstance

[UPlayer]
__vecDelDtor
SwitchController
ReceivedPlayerController

[UGameViewportClient]
__vecDelDtor
SSSwapControllers
ShowTitleSafeArea
SetConsoleTarget
CreateGameViewport
Init
FinalizeViews
RemapControllerInput
AddViewportWidgetContent
RemoveViewportWidgetContent
AddViewportWidgetForPlayer
RemoveViewportWidgetForPlayer
AddGameLayerWidget
RemoveGameLayerWidget
DetachViewportClient
Tick
SetViewportFrame
SetViewport
SetDropDetail
ConsoleCommand
GetMousePosition
SetupInitialLocalPlayer
UpdateActiveSplitscreenType
LayoutPlayers
GetSubtitleRegion
DrawTitleSafeArea
PostRender
DrawTransition
DrawTransitionMessage
NotifyPlayerAdded
NotifyPlayerRemoved
PeekTravelFailureMessages
PeekNetworkFailureMessages
VerifyPathRenderingComponents

[AGameMode]
__vecDelDtor
ReadyToEndMatch_Implementation
ReadyToStartMatch_Implementation
IsMatchInProgress
StartMatch
EndMatch
RestartGame
AbortMatch
SetMatchState
OnMatchStateSet
HandleMatchIsWaitingToStart
HandleMatchHasStarted
HandleMatchHasEnded
HandleLeavingMap
HandleMatchAborted
GetNetworkNumber
GetTravelType
Say
Broadcast
BroadcastLocalized
AddInactivePlayer
FindInactivePlayer
OverridePlayerState
SetSeamlessTravelViewTarget
PreCommitMapChange
PostCommitMapChange
NotifyPendingConnectionLost
HandleDisconnect

