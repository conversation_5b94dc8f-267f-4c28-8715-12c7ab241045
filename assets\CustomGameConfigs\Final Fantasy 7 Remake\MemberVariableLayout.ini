[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[FProperty]
ArrayDim = 0x30
DestructorLinkNext = 0x60
ElementSize = 0x34
NextRef = 0x58
Offset_Internal = 0x44
PostConstructLinkNext = 0x68
PropertyFlags = 0x38
PropertyLinkNext = 0x50
RepIndex = 0x40
RepNotifyFunc = 0x48

[FSoftClassProperty]
MetaClass = 0x78

[FArchive]
ArAllowLazyLoading = 0x2A
ArContainsCode = 0x29
ArContainsMap = 0x29
ArCustomPropertyList = 0x68
ArEngineNetVer = 0x54
ArEngineVer = 0x48
ArForceByteSwapping = 0x29
ArForceUnicode = 0x28
ArGameNetVer = 0x58
ArIgnoreArchetypeRef = 0x29
ArIgnoreClassGeneratedByRef = 0x29
ArIgnoreClassRef = 0x2A
ArIgnoreOuterRef = 0x29
ArIsCountingMemory = 0x2A
ArIsCriticalError = 0x28
ArIsError = 0x28
ArIsFilterEditorOnly = 0x2A
ArIsLoading = 0x28
ArIsModifyingWeakAndStrongReferences = 0x2A
ArIsObjectReferenceCollector = 0x2A
ArIsPersistent = 0x28
ArIsSaveGame = 0x2A
ArIsSaving = 0x28
ArIsTransacting = 0x28
ArLicenseeUE4Ver = 0x44
ArMaxSerializeSize = 0x38
ArNoDelta = 0x29
ArPortFlags = 0x30
ArRequiresLocalizationGather = 0x29
ArSerializingDefaults = 0x2C
ArShouldSkipBulkData = 0x2A
ArUE4Ver = 0x40
ArUseCustomPropertyList = 0x2B
ArWantBinaryPropertySerialization = 0x28
CookingTargetPlatform = 0x70
CustomVersionContainer = 0x60
SerializedProperty = 0x78
bCustomVersionsAreReset = 0x80

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[UFunction]
EventGraphCallOffset = 0xB0
EventGraphFunction = 0xA8
FirstPropertyToInit = 0xA0
Func = 0xB8
FunctionFlags = 0x90
NumParms = 0x94
ParmsSize = 0x96
RPCId = 0x9A
RPCResponseId = 0x9C
ReturnValueOffset = 0x98

[UField]
Next = 0x28

[AActor]
ActorHasBegunPlay = 0x13D
AttachmentReplication = 0xD0
AutoReceiveInput = 0x112
Children = 0x150
ControllingMatineeActors = 0x168
CreationTime = 0x140
CustomTimeDilation = 0x80
DetachFence = 0x360
HiddenEditorViews = 0x1A8
InitialLifeSpan = 0xCC
InputComponent = 0x118
InputPriority = 0x114
LastNetUpdateTime = 0x138
Layers = 0x180
MinNetUpdateFrequency = 0x130
NetCullDistanceSquared = 0x120
NetDormancy = 0x111
NetDriverName = 0x90
NetPriority = 0x134
NetTag = 0x124
NetUpdateFrequency = 0x12C
NetUpdateTime = 0x128
OnActorBeginOverlap = 0x1D0
OnActorEndOverlap = 0x1E0
OnActorHit = 0x270
OnBeginCursorOver = 0x1F0
OnClicked = 0x210
OnDestroyed = 0x280
OnEndCursorOver = 0x200
OnEndPlay = 0x290
OnInputTouchBegin = 0x230
OnInputTouchEnd = 0x240
OnInputTouchEnter = 0x250
OnInputTouchLeave = 0x260
OnReleased = 0x220
OnTakeAnyDamage = 0x1B0
OnTakePointDamage = 0x1C0
Owner = 0x88
ParentComponent = 0x190
PrimaryActorTick = 0x28
RemoteRole = 0x87
ReplicatedMovement = 0x98
Role = 0x110
RootComponent = 0x160
SpawnCollisionHandlingMethod = 0x13E
Tags = 0x198
TimerHandle_LifeSpanExpired = 0x178
bActorEnableCollision = 0x86
bActorInitialized = 0x13D
bActorIsBeingDestroyed = 0x13C
bActorSeamlessTraveled = 0x13D
bAllowReceiveTickEventOnDedicatedServer = 0x13D
bAllowTickBeforeBeginPlay = 0x85
bAlwaysRelevant = 0x84
bAutoDestroyWhenFinished = 0x13C
bBlockInput = 0x85
bCanBeDamaged = 0x13C
bCanBeInCluster = 0x13C
bCollideWhenPlacing = 0x13C
bEnableAutoLODGeneration = 0x13D
bExchangedRoles = 0x84
bFindCameraComponentWhenViewTarget = 0x13C
bGenerateOverlapEventsDuringLevelStreaming = 0x13C
bHasDeferredComponentRegistration = 0x86
bHasFinishedSpawning = 0x85
bHidden = 0x84
bIgnoresOriginShifting = 0x13D
bNetCheckedInitialPhysicsState = 0x86
bNetLoadOnClient = 0x85
bNetStartup = 0x84
bNetTemporary = 0x84
bNetUseOwnerRelevancy = 0x85
bOnlyRelevantToOwner = 0x84
bPendingNetUpdate = 0x85
bRelevantForNetworkReplays = 0x13C
bReplicateMovement = 0x84
bReplicates = 0x86
bRunningUserConstructionScript = 0x85
bTearOff = 0x84
bTickFunctionsRegistered = 0x85

[FEnumProperty]
Enum = 0x78
UnderlyingProp = 0x70

[UStruct]
Children = 0x38
DestructorLink = 0x68
MinAlignment = 0x44
PostConstructLink = 0x70
PropertiesSize = 0x40
PropertyLink = 0x58
RefLink = 0x60
Script = 0x48
ScriptObjectReferences = 0x78
SuperStruct = 0x30

[FDelegateProperty]
SignatureFunction = 0x70

[FMulticastDelegateProperty]
SignatureFunction = 0x70

[FObjectPropertyBase]
PropertyClass = 0x70

[FBoolProperty]
ByteMask = 0x72
ByteOffset = 0x71
FieldMask = 0x73
FieldSize = 0x70

[UScriptStruct]
CppStructOps = 0x98
StructFlags = 0x90
bPrepareCppStructOpsCompleted = 0x9C

[UWorld]
ActiveLevelCollectionIndex = 0x130
AudioDeviceHandle = 0x8F0
AudioTimeSeconds = 0x90C
AuthorityGameMode = 0xF0
BuildStreamingDataTimer = 0x500
CommittedPersistentLevelName = 0x978
DebugDrawTraceTag = 0x880
DeltaTimeSeconds = 0x910
ExtraReferencedObjects = 0x68
LastTimeUnbuiltLightingWasEncountered = 0x8F8
NextSwitchCountdown = 0x960
NextURL = 0x950
NumInvalidReflectionCaptureComponents = 0x984
NumLightingUnbuiltObjects = 0x980
NumTextureStreamingDirtyResources = 0x98C
NumTextureStreamingUnbuiltComponents = 0x988
OriginOffsetThisFrame = 0x930
PauseDelay = 0x914
PerModuleDataObjects = 0x78
PlayerNum = 0x864
PreparingLevelNames = 0x968
RealTimeSeconds = 0x908
StreamingLevelsPrefix = 0x98
StreamingVolumeUpdateDelay = 0x86C
TimeSeconds = 0x900
UnpausedTimeSeconds = 0x904
ViewLocationsRenderedLastFrame = 0xD0
bActorsInitialized = 0x990
bAggressiveLOD = 0x990
bAllowAudioPlayback = 0x990
bAreConstraintsDirty = 0x990
bBegunPlay = 0x990
bDebugDrawAllTraceTags = 0x888
bDebugFrameStepExecution = 0x990
bDebugPauseExecution = 0x990
bDoDelayedUpdateCullDistanceVolumes = 0x873
bDropDetail = 0x990
bHack_Force_UsesGameHiddenFlags_True = 0x878
bInTick = 0x750
bIsBuilt = 0x751
bIsCameraMoveableWhenPaused = 0x990
bIsDefaultLevel = 0x990
bIsLevelStreamingFrozen = 0x870
bIsRunningConstructionScript = 0x879
bIsTearingDown = 0x990
bIsWorldInitialized = 0x868
bKismetScriptError = 0x990
bMatchStarted = 0x990
bPlayersOnly = 0x990
bPlayersOnlyPending = 0x990
bPostTickComponentUpdate = 0x860
bRequestedBlockOnAsyncLoading = 0x990
bRequiresHitProxies = 0x388
bShouldForceUnloadStreamingLevels = 0x871
bShouldForceVisibleStreamingLevels = 0x872
bShouldSimulatePhysics = 0x87A
bShouldTick = 0x389
bStartup = 0x990
bStreamingDataDirty = 0x4F8
bTickNewlySpawned = 0x752
bTriggerPostLoadMap = 0xE0
bWorldWasLoadedThisTick = 0xE0

[FSetProperty]
ElementProp = 0x70

[UClass]
ClassAddReferencedObjects = 0xB0
ClassCastFlags = 0xC0
ClassConfigName = 0xD8
ClassConstructor = 0xA0
ClassDefaultObject = 0x100
ClassFlags = 0xBC
ClassGeneratedBy = 0xD0
ClassUnique = 0xB8
ClassVTableHelperCtorCaller = 0xA8
ClassWithin = 0xC8
Interfaces = 0x1A8
NetFields = 0xF0
bCooked = 0xB8

[AGameModeBase]
DefaultPlayerName = 0x3C8
GameSession = 0x3B8
GameSessionClass = 0x378
HUDClass = 0x398
OptionsString = 0x368
Pausers = 0x3E8
PlayerStateClass = 0x390
SpectatorClass = 0x3A8
bPauseable = 0x3E0
bStartPlayersAsSpectators = 0x3E0
bUseSeamlessTravel = 0x3E0

[UEnum]
CppForm = 0x50
CppType = 0x30
EnumDisplayNameFn = 0x58
Names = 0x40

[FMapProperty]
KeyProp = 0x70
ValueProp = 0x78

[FStructProperty]
Struct = 0x70

[FArrayProperty]
Inner = 0x70

[FByteProperty]
Enum = 0x70

[FClassProperty]
MetaClass = 0x78

[FInterfaceProperty]
InterfaceClass = 0x70

