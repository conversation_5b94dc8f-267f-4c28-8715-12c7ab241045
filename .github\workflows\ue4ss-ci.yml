# This workflow automates the build process for all modes of the UE4SS project whenever a pull request is made.
# It dynamically calculates the build matrix based on the modes defined in the project and reports artifacts to the PR comments.

name: Build All Modes
permissions:
    contents: write
    pull-requests: write # For adding comments to PR.
on:
  pull_request:
    branches:
        - 'main'
    paths-ignore:
        - 'README.md'
        - 'LICENSE'
        - 'CNAME'
        - '.github/workflows/pushdocs.yml'
        - '.github/pull_request_template.md'
        - '.github/ISSUE_TEMPLATE/**'
        - 'assets/**'
        - 'docs/**'
        - 'docs-export/**'
        - 'docs-repo-template/**'

# Ensure that rapid pushes to the pull request branch don't trigger this workflow multiple times.
# We only care about executing this workflow for that 'latest' commit on a PR.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Dynamically calculate which modes to build.
  # We also need to determine which modes should upload artifacts.
  calculate-matrix:
    name: Calculate job matrix
    runs-on: ubuntu-latest
    outputs:
      # JSON array with the following schema:
      # [
      #   {
      #     "mode": "Game__Shipping__Win64",
      #     "artifact": false
      #   },
      #   ...
      # ]
      matrix: ${{ steps.calculate-artifacts.outputs.matrix }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive # Clone the submodule so we can query xmake on the entire project.
          token: ${{ secrets.UEPSEUDO_PAT }}

        # Build modes are supplied by xmake, so we have to download and setup the xmake environment.
      - name: Setup xmake
        uses: xmake-io/github-action-setup-xmake@v1
        with:
          xmake-version: "2.9.3"
          actions-cache-folder: '.xmake-cache' # This auto-cache functionality only works on linux runners.

        # `xmake ci --dump=modes` returns a JSON array of all UE4SS modes that xmake knows how to build.
      - name: Get xmake Modes
        id: get-xmake-modes
        shell: pwsh
        run: |
          $modes = (xmake ci --dump=modes)
          echo "all_modes=$modes" >> $env:GITHUB_OUTPUT
        
        # Construct the build matrix by adding `"artifact": true/false` to each mode.
      - name: Finalize Mode Matrix  
        uses: actions/github-script@v7
        id: calculate-artifacts
        env:
          ALL_MODES: '${{steps.get-xmake-modes.outputs.all_modes}}'
          REQUESTED_MODES: '["Game__Shipping__Win64", "Game__Debug__Win64"]'
        with:
          result-encoding: string
          script: |
            const { ALL_MODES, REQUESTED_MODES } = process.env;
            const requestedModes = JSON.parse(REQUESTED_MODES)
            const modeSchema = JSON.parse(ALL_MODES).map(m => ({mode: m, artifact: requestedModes.includes(m) }));
            core.setOutput('matrix', JSON.stringify(modeSchema));

  # Build UE4SS for each mode we received from the `calculate_matrix` job.
  build:
    name: ${{ matrix.mode }}
    needs: [ calculate-matrix ] # Ensure the mode/artifact matrix has been generated before we launch this job.
    strategy:
      fail-fast: false # Disable fast failing because we want to cancel other modes if one fails.
      matrix: 
        # Use our generated matrix to populate the `include` array.
        include: ${{ fromJSON(needs.calculate-matrix.outputs.matrix) }}
    # Run our reusable workflow for each mode in our matrix.
    uses: ./.github/workflows/msvc_build_ue4ss.yml
    secrets: inherit # Inherit secrets from this workflow to safely pass `secrets.UE4SS_PAT` to our reusable workflow.
    with:
      build-mode: ${{ matrix.mode }}
      commit-sha: ${{ github.sha }} # This is the SHA of the PR merge result, not the HEAD of the PR branch!
      should-upload-artifact: ${{ matrix.artifact }}
      # List that declares which xmake targets should have their output uploaded as an artifact.
      # Available targets should be in the list generated by `xmake ci --dump=targets`.
      # This specifies that the `UE4SS` target should upload its target file and symbol file.
      # Ex. UE4SS.dll + UE4SS.pdb
      artifact-list: '["UE4SS"]'
      artifact-retention-days: 14
  
  # Report artifacts as a pull request comment.
  report-artifacts:
    if: ${{ always() && contains(needs.calculate-matrix.result, 'success') }}
    needs: [calculate-matrix, build]
    runs-on: ubuntu-latest
    steps:
      - name: "Report Artifacts"
        uses: bitonality/PRtifact@v0.1.0
        with:
          comment-mode: 'CreateOrUpdate'
