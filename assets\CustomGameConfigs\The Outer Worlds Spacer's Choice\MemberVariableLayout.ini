[FField]
ClassPrivate = 0x8
FlagsPrivate = 0x30
NamePrivate = 0x28
Next = 0x20
Owner = 0x10

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[FSoftClassProperty]
MetaClass = 0x80

[AGameModeBase]
DefaultPlayerName = 0x328
GameSession = 0x310
GameSessionClass = 0x2C8
HUDClass = 0x2E8
OptionsString = 0x2B8
PlayerStateClass = 0x2E0
ServerStatReplicator = 0x320
ServerStatReplicatorClass = 0x308
SpectatorClass = 0x2F8
bCreateCharacterHUD = 0x344
bPauseable = 0x348
bStartPlayersAsSpectators = 0x348
bUseSeamlessTravel = 0x340

[FSetProperty]
ElementProp = 0x78
SetLayout = 0x80

[UClass]
ClassAddReferencedObjects = 0xC8
ClassCastFlags = 0xD8
ClassConfigName = 0xF0
ClassConstructor = 0xB8
ClassDefaultObject = 0x120
ClassFlags = 0xD4
ClassGeneratedBy = 0xE8
ClassUnique = 0xD0
ClassVTableHelperCtorCaller = 0xC0
ClassWithin = 0xE0
FirstOwnedClassRep = 0x118
FuncMap = 0x138
Interfaces = 0x1E0
NetFields = 0x108
SparseClassData = 0x128
SparseClassDataStruct = 0x130
SuperFuncMap = 0x188
bCooked = 0xD0

[FDelegateProperty]
SignatureFunction = 0x78

[UEnum]
CppForm = 0x58
CppType = 0x38
EnumDisplayNameFn = 0x60
EnumFlags = 0x5C
Names = 0x48

[UFunction]
EventGraphCallOffset = 0xD8
EventGraphFunction = 0xD0
FirstPropertyToInit = 0xC8
Func = 0xE0
FunctionFlags = 0xB8
NumParms = 0xBC
ParmsSize = 0xBE
RPCId = 0xC2
RPCResponseId = 0xC4
ReturnValueOffset = 0xC0

[UField]
Next = 0x30

[FMulticastDelegateProperty]
SignatureFunction = 0x78

[FArchiveState]
ArAllowLazyLoading = 0x2A
ArContainsCode = 0x29
ArContainsMap = 0x29
ArCustomPropertyList = 0x68
ArEngineNetVer = 0x54
ArEngineVer = 0x48
ArForceByteSwapping = 0x29
ArForceUnicode = 0x28
ArGameNetVer = 0x58
ArIgnoreArchetypeRef = 0x29
ArIgnoreClassGeneratedByRef = 0x2A
ArIgnoreClassRef = 0x2A
ArIgnoreOuterRef = 0x2A
ArIsCountingMemory = 0x2A
ArIsCriticalError = 0x29
ArIsError = 0x29
ArIsFilterEditorOnly = 0x2B
ArIsLoading = 0x28
ArIsModifyingWeakAndStrongReferences = 0x2A
ArIsNetArchive = 0x2B
ArIsObjectReferenceCollector = 0x2A
ArIsPersistent = 0x28
ArIsSaveGame = 0x2B
ArIsSaving = 0x28
ArIsTextFormat = 0x28
ArIsTransacting = 0x28
ArLicenseeUE4Ver = 0x44
ArMaxSerializeSize = 0x38
ArNoDelta = 0x29
ArNoIntraPropertyDelta = 0x2A
ArPortFlags = 0x30
ArRequiresLocalizationGather = 0x29
ArSerializingDefaults = 0x2C
ArShouldSkipBulkData = 0x2B
ArUE4Ver = 0x40
ArUseCustomPropertyList = 0x2B
ArUseUnversionedPropertySerialization = 0x28
ArWantBinaryPropertySerialization = 0x28
CookingTargetPlatform = 0x70
CustomVersionContainer = 0x60
NextProxy = 0x90
SerializedProperty = 0x78
bCustomVersionsAreReset = 0x88

[FProperty]
ArrayDim = 0x38
DestructorLinkNext = 0x68
ElementSize = 0x3C
NextRef = 0x60
Offset_Internal = 0x4C
PostConstructLinkNext = 0x70
PropertyFlags = 0x40
PropertyLinkNext = 0x58
RepIndex = 0x48
RepNotifyFunc = 0x50

[FWorldContext]
AudioDeviceID = 0x250
ContextHandle = 0xB0
CustomDescription = 0x258
ExternalReferences = 0x270
GameViewport = 0x210
LastRemoteURL = 0x138
LastURL = 0xD0
LevelsToLoadForPendingMapChange = 0x1B8
PIEAccumulatedTickSeconds = 0x26C
PIEFixedTickSeconds = 0x268
PIEInstance = 0x230
PIEPrefix = 0x238
PendingMapChangeFailureDescription = 0x1D8
RunAsDedicated = 0x24C
ThisCurrentWorld = 0x280
TravelType = 0xC8
TravelURL = 0xB8
bShouldCommitPendingMapChange = 0x1E8
bWaitingOnOnlineSubsystem = 0x24D

[ULocalPlayer]
AspectRatioAxisConstraint = 0x9C
CachedUniqueNetId = 0x50
ControllerId = 0xC0
LastViewLocation = 0x90
Origin = 0x80
Size = 0x88
SlateOperations = 0x1A8
ViewportClient = 0x78
bSentSplitJoin = 0xA8

[FByteProperty]
Enum = 0x78

[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20

[FBoolProperty]
ByteMask = 0x7A
ByteOffset = 0x79
FieldMask = 0x7B
FieldSize = 0x78

[FObjectPropertyBase]
PropertyClass = 0x78

[FClassProperty]
MetaClass = 0x80

[FInterfaceProperty]
InterfaceClass = 0x78

[FArrayProperty]
ArrayFlags = 0x80
Inner = 0x78

[FMapProperty]
KeyProp = 0x78
MapFlags = 0xA0
MapLayout = 0x88
ValueProp = 0x80

[FStructProperty]
Struct = 0x78

[FEnumProperty]
Enum = 0x80
UnderlyingProp = 0x78

[UStruct]
ChildProperties = 0x58
Children = 0x50
DestructorLink = 0x88
MinAlignment = 0x64
PostConstructLink = 0x90
PropertiesSize = 0x60
PropertyLink = 0x78
RefLink = 0x80
Script = 0x68
ScriptAndPropertyObjectReferences = 0x98
SuperStruct = 0x48
UnresolvedScriptProperties = 0xA8

[FFieldPathProperty]
PropertyClass = 0x78

[UScriptStruct]
CppStructOps = 0xC0
StructFlags = 0xB8
bPrepareCppStructOpsCompleted = 0xBC

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[AActor]
ActorHasBegunPlay = 0x7D
AttachmentReplication = 0xC8
AutoReceiveInput = 0x11B
Children = 0x148
ConstructContext = 0x220
ControllingMatineeActors = 0x160
CreationTime = 0xC0
CullSources = 0x210
CustomTimeDilation = 0xBC
DefaultUpdateOverlapsMethodDuringLevelStreaming = 0x7F
DetachFence = 0x2A8
InitialLifeSpan = 0xB8
InputComponent = 0x120
InputPriority = 0x11C
LastRenderTime = 0x13C
Layers = 0x178
MinNetUpdateFrequency = 0x134
NetCullDistanceSquared = 0x128
NetDormancy = 0x119
NetDriverName = 0x110
NetPriority = 0x138
NetTag = 0x12C
NetUpdateFrequency = 0x130
OnActorBeginOverlap = 0x1E1
OnActorEndOverlap = 0x1E2
OnActorHit = 0x1EB
OnBeginCursorOver = 0x1E3
OnClicked = 0x1E5
OnDestroyed = 0x1F0
OnEndCursorOver = 0x1E4
OnEndPlay = 0x200
OnInputTouchBegin = 0x1E7
OnInputTouchEnd = 0x1E8
OnInputTouchEnter = 0x1E9
OnInputTouchLeave = 0x1EA
OnReleased = 0x1E6
OnSetStasis = 0x1D0
OnTakeAnyDamage = 0x1C8
OnTakePointDamage = 0x1C9
OnTakeRadialDamage = 0x1E0
Owner = 0x108
ParentComponent = 0x188
PrimaryActorTick = 0x30
RemoteRole = 0x80
ReplicatedMovement = 0x84
Role = 0x118
RootComponent = 0x158
SpawnCollisionHandlingMethod = 0x11A
StasisLogicDataAsset = 0x198
Tags = 0x1B8
TemporaryStasisSources = 0x1A0
TimerHandle_LifeSpanExpired = 0x170
UpdateOverlapsMethodDuringLevelStreaming = 0x7E
bActorBeginningPlayFromLevelStreaming = 0x7C
bActorEnableCollision = 0x7D
bActorInitialized = 0x7C
bActorIsBeingConstructed = 0x190
bActorIsBeingDestroyed = 0x7D
bActorSeamlessTraveled = 0x7B
bActorWantsDestroyDuringBeginPlay = 0x7D
bAllowChildActorsToSaveGameState = 0x191
bAllowReceiveTickEventOnDedicatedServer = 0x7C
bAllowTickBeforeBeginPlay = 0x79
bAlwaysRelevant = 0x78
bAutoDestroyWhenFinished = 0x79
bBlockInput = 0x7A
bCanBeDamaged = 0x7A
bCanBeInCluster = 0x7C
bCollideWhenPlacing = 0x7A
bCreatedFromPool = 0x190
bDeferredConstruction = 0x191
bDisableCollisionInStasis = 0x1B0
bEnableAutoLODGeneration = 0x7A
bExchangedRoles = 0x79
bExcludeFromLightmap = 0x7B
bFindCameraComponentWhenViewTarget = 0x7A
bForceNetAddressable = 0x78
bGenerateOverlapEventsDuringLevelStreaming = 0x7A
bHasDeferredComponentRegistration = 0x7C
bHasFinishedSpawning = 0x7C
bHasLatentActions = 0x7B
bHidden = 0x78
bIgnoresOriginShifting = 0x7A
bInPool = 0x190
bIncludeInTerrainLOD = 0x7A
bIsEditorOnlyActor = 0x7B
bIsInFixedStasis = 0x190
bIsInInteriorLevel = 0x7B
bIsInStasis = 0x190
bIsPrefab = 0x191
bNetCheckedInitialPhysicsState = 0x7C
bNetLoadOnClient = 0x79
bNetStartup = 0x78
bNetTemporary = 0x78
bNetUseOwnerRelevancy = 0x79
bOnlyRelevantToOwner = 0x78
bRelevantForLevelBounds = 0x79
bRelevantForNetworkReplays = 0x79
bReplayRewindable = 0x79
bReplicateMovement = 0x78
bReplicates = 0x7B
bRunningUserConstructionScript = 0x7D
bSavedStateWasRestored = 0x191
bShouldReturnToPool = 0x190
bShouldSaveGameState = 0x191
bShouldUpdateNavMeshAfterMove = 0x191
bSpawnedAtRuntime = 0x191
bTearOff = 0x78
bTickFunctionsRegistered = 0x7C
bUnhideAfterLevelLoad = 0x7B
bUsesRefCountingForActorHiddenInGame = 0x190
bVisibleInHLOD = 0x7B
bWasInStasisAtTimeOfLastSave = 0x190

[UPlayer]
ConfiguredInternetSpeed = 0x44
ConfiguredLanSpeed = 0x48
CurrentNetSpeed = 0x40

[UWorld]
ActiveLevelCollectionIndex = 0x1C0
ActorSpawnTickFunction = 0x610
AsyncPreRegisterLevelStreamingTickEvent = 0x740
AudioTimeSeconds = 0x6AC
AuthorityGameMode = 0x120
BuildStreamingDataTimer = 0x458
CameraLocationThisFrame = 0x6C4
CleanupWorldTag = 0x75C
CommittedPersistentLevelName = 0x730
CurrentDeferredActor = 0x218
DeferredActors = 0x160
DeltaTimeSeconds = 0x6B0
EngineLoopTickStart = 0x6B8
ExtraReferencedObjects = 0x70
LastTimeUnbuiltLightingWasEncountered = 0x698
NextSwitchCountdown = 0x6F4
NextURL = 0x708
NumStreamingLevelsBeingLoaded = 0x702
OriginOffsetThisFrame = 0x6E8
PauseDelay = 0x6C0
PerModuleDataObjects = 0x80
PlayerNum = 0x658
PreparingLevelNames = 0x720
RealTimeSeconds = 0x6A8
StreamingLevelsPrefix = 0xC8
StreamingVolumeUpdateDelay = 0x65C
TimeSeconds = 0x6A0
URL = 0x508
UnpausedTimeSeconds = 0x6A4
ViewLocationsRenderedLastFrame = 0x100
bActorsInitialized = 0x114
bAggressiveLOD = 0x114
bAllowAudioPlayback = 0x116
bAreConstraintsDirty = 0x116
bBegunPlay = 0x115
bDebugPauseExecution = 0x115
bDoDelayedUpdateCullDistanceVolumes = 0x114
bDropDetail = 0x114
bFlushingLevelStreaming = 0x718
bInTick = 0x113
bIsBuilt = 0x113
bIsCameraMoveableWhenPaused = 0x116
bIsDefaultLevel = 0x114
bIsLevelStreamingFrozen = 0x113
bIsRunningConstructionScript = 0x114
bIsTearingDown = 0x115
bIsWorldInitialized = 0x113
bKismetScriptError = 0x115
bMarkedObjectsPendingKill = 0x758
bMatchStarted = 0x115
bMaterialParameterCollectionInstanceNeedsDeferredUpdate = 0x117
bPlayersOnly = 0x115
bPlayersOnlyPending = 0x115
bPostTickComponentUpdate = 0x113
bRequestedBlockOnAsyncLoading = 0x114
bRequiresHitProxies = 0x116
bShouldForceUnloadStreamingLevels = 0x116
bShouldForceVisibleStreamingLevels = 0x116
bShouldSimulatePhysics = 0x114
bShouldTick = 0x116
bStartup = 0x115
bStreamingDataDirty = 0x116
bTickNewlySpawned = 0x113
bTriggerPostLoadMap = 0x113
bWorldWasLoadedThisTick = 0x113

[UGameViewportClient]
ActiveSplitscreenType = 0x78
AudioDeviceHandle = 0x108
CurrentBufferVisualizationMode = 0x140
CursorWidgets = 0x1F8
DebugProperties = 0x50
EngineShowFlags = 0xC8
GameLayerManagerPtr = 0x130
HardwareCursorCache = 0x158
HardwareCursors = 0x1A8
HighResScreenshotDialog = 0x148
MaxSplitscreenPlayers = 0x70
MouseCaptureMode = 0x37A
MouseLockMode = 0x37C
ScreenshotSize = 0xBC
SplitscreenInfo = 0x60
StatHitchesData = 0x368
StatSystemMemoryData = 0x370
StatUnitData = 0x360
ViewModeIndex = 0xC4
Viewport = 0xF8
ViewportConsole = 0x48
ViewportFrame = 0x100
ViewportOverlayWidget = 0x120
Window = 0x110
World = 0x80
bDisableSplitScreenOverride = 0x378
bDisableWorldRendering = 0x74
bHasAudioFocus = 0x10C
bHideCursorDuringCapture = 0x37B
bIgnoreInput = 0x379
bIsMouseOverClient = 0x37D
bIsPlayInEditorViewport = 0x74
bShowTitleSafeZone = 0x74
bSuppressTransitionMessage = 0x90
bTriggerScreenshotOnGPU = 0xB8
bUseSoftwareCursorWidgets = 0x248

[AGameMode]
EngineMessageClass = 0x380
InactivePlayerArray = 0x388
InactivePlayerStateLifeSpan = 0x398
MatchState = 0x360
MaxInactivePlayers = 0x39C
MinRespawnDelay = 0x378
NumBots = 0x374
NumPlayers = 0x370
NumSpectators = 0x36C
NumTravellingPlayers = 0x37C
bDelayedStart = 0x368
bHandleDedicatedServerReplays = 0x3A0

