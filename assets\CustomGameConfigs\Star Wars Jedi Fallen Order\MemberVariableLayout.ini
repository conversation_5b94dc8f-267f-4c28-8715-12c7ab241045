[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[FProperty]
ArrayDim = 0x30
DestructorLinkNext = 0x60
ElementSize = 0x34
NextRef = 0x58
Offset_Internal = 0x44
PostConstructLinkNext = 0x68
PropertyFlags = 0x38
PropertyLinkNext = 0x50
RepIndex = 0x40
RepNotifyFunc = 0x48

[FSoftClassProperty]
MetaClass = 0x78

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[UFunction]
EventGraphCallOffset = 0xA8
EventGraphFunction = 0xA0
FirstPropertyToInit = 0x98
Func = 0xB0
FunctionFlags = 0x88
NumParms = 0x8C
ParmsSize = 0x8E
RPCId = 0x92
RPCResponseId = 0x94
ReturnValueOffset = 0x90

[UField]
Next = 0x28

[FEnumProperty]
Enum = 0x78
UnderlyingProp = 0x70

[UStruct]
Children = 0x38
DestructorLink = 0x68
MinAlignment = 0x44
PostConstructLink = 0x70
PropertiesSize = 0x40
PropertyLink = 0x58
RefLink = 0x60
Script = 0x48
ScriptObjectReferences = 0x78
SuperStruct = 0x30

[FDelegateProperty]
SignatureFunction = 0x70

[FMulticastDelegateProperty]
SignatureFunction = 0x70

[FObjectPropertyBase]
PropertyClass = 0x70

[FBoolProperty]
ByteMask = 0x72
ByteOffset = 0x71
FieldMask = 0x73
FieldSize = 0x70

[UScriptStruct]
CppStructOps = 0x90
StructFlags = 0x88
bPrepareCppStructOpsCompleted = 0x8C

[UWorld]
ActiveLevelCollectionIndex = 0x180
AudioDeviceHandle = 0x748
AudioTimeSeconds = 0x764
BuildStreamingDataTimer = 0x4A8
CommittedPersistentLevelName = 0x7D0
DebugDrawTraceTag = 0x6D8
DeltaTimeSeconds = 0x768
ExtraReferencedObjects = 0x68
LastTimeUnbuiltLightingWasEncountered = 0x750
NextSwitchCountdown = 0x7B8
NextURL = 0x7A8
NumLightingUnbuiltObjects = 0x7D8
NumTextureStreamingDirtyResources = 0x7E4
NumTextureStreamingUnbuiltComponents = 0x7E0
NumUnbuiltReflectionCaptures = 0x7DC
OriginOffsetThisFrame = 0x788
PauseDelay = 0x76C
PerModuleDataObjects = 0x78
PlayerNum = 0x6BC
PreparingLevelNames = 0x7C0
RealTimeSeconds = 0x760
StreamingLevelsPrefix = 0xE8
StreamingVolumeUpdateDelay = 0x6C4
TimeSeconds = 0x758
UnpausedTimeSeconds = 0x75C
ViewLocationsRenderedLastFrame = 0x120
bActorsInitialized = 0x7E8
bAggressiveLOD = 0x7E8
bAllowAudioPlayback = 0x7E8
bAreConstraintsDirty = 0x7E8
bBegunPlay = 0x7E8
bCleanedUpWorld = 0x7E8
bDebugDrawAllTraceTags = 0x6E0
bDebugFrameStepExecution = 0x7E8
bDebugPauseExecution = 0x7E8
bDoDelayedUpdateCullDistanceVolumes = 0x6CC
bDropDetail = 0x7E8
bHack_Force_UsesGameHiddenFlags_True = 0x6D4
bInTick = 0x5A8
bIsBuilt = 0x5A9
bIsCameraMoveableWhenPaused = 0x7E8
bIsDefaultLevel = 0x7E8
bIsLevelStreamingFrozen = 0x6C8
bIsRunningConstructionScript = 0x6D5
bIsTearingDown = 0x7E8
bIsWorldInitialized = 0x6C0
bKismetScriptError = 0x7E8
bMarkedObjectsPendingKill = 0x7E8
bMatchStarted = 0x7E8
bMaterialParameterCollectionInstanceNeedsDeferredUpdate = 0x6CB
bPlayersOnly = 0x7E8
bPlayersOnlyPending = 0x7E8
bPostTickComponentUpdate = 0x6B8
bRequestedBlockOnAsyncLoading = 0x7E8
bRequiresHitProxies = 0x3D8
bShouldForceUnloadStreamingLevels = 0x6C9
bShouldForceVisibleStreamingLevels = 0x6CA
bShouldSimulatePhysics = 0x6D6
bShouldTick = 0x3D9
bStartup = 0x7E8
bStreamingDataDirty = 0x4A0
bTickNewlySpawned = 0x5AA
bTriggerPostLoadMap = 0x130
bWorldWasLoadedThisTick = 0x130

[FSetProperty]
ElementProp = 0x70

[UClass]
ClassAddReferencedObjects = 0xA8
ClassConfigName = 0xE0
ClassConstructor = 0x98
ClassDefaultObject = 0x100
ClassFlags = 0xB8
ClassGeneratedBy = 0xC8
ClassUnique = 0xB0
ClassVTableHelperCtorCaller = 0xA0
ClassWithin = 0xC8
Interfaces = 0x1B8
NetFields = 0xF8
UberGraphFramePointerProperty = 0xD8
bCooked = 0xB0

[UEnum]
CppForm = 0x50
CppType = 0x30
EnumDisplayNameFn = 0x58
Names = 0x40

[FMapProperty]
KeyProp = 0x70
ValueProp = 0x78

[FStructProperty]
Struct = 0x70

[FArrayProperty]
Inner = 0x70

[FByteProperty]
Enum = 0x70

[FClassProperty]
MetaClass = 0x78

[FInterfaceProperty]
InterfaceClass = 0x70

