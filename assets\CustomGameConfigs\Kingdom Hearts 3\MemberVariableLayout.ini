[UObjectBase]
ClassPrivate = 0x18
InternalIndex = 0xC
NamePrivate = 0x20
ObjectFlags = 0x8
OuterPrivate = 0x28

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[FProperty]
ArrayDim = 0x48
DestructorLinkNext = 0x80
ElementSize = 0x4C
NextRef = 0x78
Offset_Internal = 0x68
PostConstructLinkNext = 0x88
PropertyFlags = 0x50
PropertyLinkNext = 0x70
RepIndex = 0x58
RepNotifyFunc = 0x60

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[UFunction]
EventGraphCallOffset = 0xD0
EventGraphFunction = 0xB8
FirstPropertyToInit = 0xB0
Func = 0xC8
FunctionFlags = 0xA0
NumParms = 0xA6
ParmsSize = 0xA8
RPCId = 0xAC
RPCResponseId = 0xAE
RepOffset = 0xA4
ReturnValueOffset = 0xAA

[UField]
; 0x10 bytes between the end of UObject and UField::Next.
Next = 0x40

[FEnumProperty]
Enum = 0x98
UnderlyingProp = 0x90

[UStruct]
Children = 0x50
DestructorLink = 0x80
MinAlignment = 0x5C
PostConstructLink = 0x88
PropertiesSize = 0x58
PropertyLink = 0x70
RefLink = 0x78
Script = 0x60
ScriptObjectReferences = 0x90
SuperStruct = 0x48

[FDelegateProperty]
SignatureFunction = 0x90

[FMulticastDelegateProperty]
SignatureFunction = 0x90

[FObjectPropertyBase]
PropertyClass = 0x90

[FBoolProperty]
ByteMask = 0x92
ByteOffset = 0x91
FieldMask = 0x93
FieldSize = 0x90

[UScriptStruct]
CppStructOps = 0xA8
StructFlags = 0xA0
bPrepareCppStructOpsCompleted = 0xA4

; This struct has not been converted yet.
; You need to add +0x8 to each of these entries.
; UWorld is however not used for most UE4SS functionality so I'm not gonna bother with it for now.
[UWorld]
AudioDeviceHandle = 0x8F0
AudioTimeSeconds = 0x90C
BuildStreamingDataTimer = 0x500
CommittedPersistentLevelName = 0x978
DebugDrawTraceTag = 0x880
DeltaTimeSeconds = 0x910
ExtraReferencedObjects = 0x68
FullPurgeTriggered = 0x86C
LastTimeUnbuiltLightingWasEncountered = 0x8F8
NextSwitchCountdown = 0x960
NextURL = 0x950
NumInvalidReflectionCaptureComponents = 0x984
NumLightingUnbuiltObjects = 0x980
NumTextureStreamingDirtyResources = 0x98C
NumTextureStreamingUnbuiltComponents = 0x988
OriginOffsetThisFrame = 0x930
PauseDelay = 0x914
PerModuleDataObjects = 0x78
PlayerNum = 0x864
PreparingLevelNames = 0x968
RealTimeSeconds = 0x908
StreamingLevelsPrefix = 0x98
StreamingVolumeUpdateDelay = 0x870
TimeSeconds = 0x900
TimeSinceLastPendingKillPurge = 0x868
UnpausedTimeSeconds = 0x904
ViewLocationsRenderedLastFrame = 0xD0
bActorsInitialized = 0x990
bAggressiveLOD = 0x990
bAllowAudioPlayback = 0x990
bAreConstraintsDirty = 0x990
bBegunPlay = 0x990
bDebugDrawAllTraceTags = 0x888
bDebugFrameStepExecution = 0x990
bDebugPauseExecution = 0x990
bDoDelayedUpdateCullDistanceVolumes = 0x877
bDropDetail = 0x990
bHack_Force_UsesGameHiddenFlags_True = 0x87C
bInTick = 0x750
bIsBuilt = 0x751
bIsCameraMoveableWhenPaused = 0x990
bIsDefaultLevel = 0x990
bIsLevelStreamingFrozen = 0x874
bIsRunningConstructionScript = 0x87D
bIsTearingDown = 0x990
bIsWorldInitialized = 0x86E
bKismetScriptError = 0x990
bMatchStarted = 0x990
bPlayersOnly = 0x990
bPlayersOnlyPending = 0x990
bPostTickComponentUpdate = 0x860
bRequestedBlockOnAsyncLoading = 0x990
bRequiresHitProxies = 0x388
bShouldDelayGarbageCollect = 0x86D
bShouldForceUnloadStreamingLevels = 0x875
bShouldForceVisibleStreamingLevels = 0x876
bShouldSimulatePhysics = 0x87E
bShouldTick = 0x389
bStartup = 0x990
bStreamingDataDirty = 0x4F8
bTickNewlySpawned = 0x752
bTriggerPostLoadMap = 0xE0
bWorldWasLoadedThisTick = 0xE0

[FSetProperty]
ElementProp = 0x90

[UClass]
ClassAddReferencedObjects = 0xC0
ClassCastFlags = 0xD0
ClassConfigName = 0xE8
ClassConstructor = 0xB0
; ClassDefaultObject has been confirmed correct.
ClassDefaultObject = 0x118
ClassFlags = 0xCC
ClassGeneratedBy = 0xE0
ClassUnique = 0xC8
ClassVTableHelperCtorCaller = 0xB8
; ClassWithin has been confirmed correct.
ClassWithin = 0xD8
; There's something odd here.
; Interfaces is supposed to be at 0x218 but it's at 0x1D0.
; This suggests that the devs for some reason removed stuff from the original struct.
; Something between ClassDefaultObject and Interfaces is different.
; CppTypeInfo (0x120) has been confirmed correct (not included in this file).
; FuncMap (0x128) has been confirmed correct (not included in this file).
; ParentFuncMap seems to be missing or is inlined, smaller and empty maybe ?
; This will only be a problem if there are any structs that inherit from UClass in this file, which I dont' think there are at the moment.
; If there is in the future, then you'll have to adjust those offsets to account for the missing/smaller stuff in UClass.
Interfaces = 0x1D0
; NetFields has been confirmed correct.
NetFields = 0x108
bCooked = 0xF0

[UEnum]
CppForm = 0x68
CppType = 0x48
EnumDisplayNameFn = 0x70
Names = 0x58

[FMapProperty]
KeyProp = 0x90
ValueProp = 0x98

[FStructProperty]
Struct = 0x90

[FArrayProperty]
Inner = 0x90

[FByteProperty]
Enum = 0x90

[FClassProperty]
MetaClass = 0x98

[FInterfaceProperty]
InterfaceClass = 0x90

