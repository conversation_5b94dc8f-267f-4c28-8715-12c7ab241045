[UObjectBase]
__vecDelDtor
RegisterDependencies
DeferredRegister

[UObjectBaseUtility]
__vecDelDtor
CanBeClusterRoot
CanBeInCluster
CreateCluster
OnClusterMarkedAsPendingKill
AddToCluster

[UObject]
__vecDelDtor
GetDetailedInfoInternal
PostInitProperties
PostCDOContruct
PreSaveRoot
PostSaveRoot
PreSave
Modify
PostLoad
PostLoadSubobjects
BeginDestroy
IsReadyForFinishDestroy
FinishDestroy
Serialize
; void Serialize(param_count: 1, );
Serialize_2
ShutdownAfterError
PostInterpChange
PostRename
PostDuplicate
; void PostDuplicate(param_count: 1, );
PostDuplicate_2
NeedsLoadForClient
NeedsLoadForServer
NeedsLoadForTargetPlatform
NeedsLoadForEditorGame
IsEditorOnly
IsPostLoadThreadSafe
GetPreloadDependencies
GetPrestreamPackages
ExportCustomProperties
ImportCustomProperties
PostEditImport
PostReloadConfig
Rename
GetDesc
GetWorld
GetNativePropertyValues
GetResourceSizeEx
GetExporterName
GetRestoreForUObjectOverwrite
AreNativePropertiesIdenticalTo
GetAssetRegistryTags
IsAsset
NotifyObjectReferenceEliminated
GetPrimaryAssetId
IsLocalizedResource
IsSafeForRootSet
TagSubobjects
GetLifetimeReplicatedProps
IsNameStableForNetworking
IsFullNameStableForNetworking
IsSupportedForNetworking
GetSubobjectsWithStableNamesForNetworking
PreNetReceive
PostNetReceive
PostRepNotifies
PreDestroyFromReplication
BuildSubobjectMapping
GetConfigOverridePlatform
ProcessEvent
GetFunctionCallspace
CallRemoteFunction
ProcessConsoleExec
RegenerateClass
MarkAsEditorOnlySubobject
CheckDefaultSubobjectsInternal
UnknownFunc_1
UnknownFunc_2
UnknownFunc_3
UnknownFunc_4
UnknownFunc_5
UnknownFunc_6
UnknownFunc_7
UnknownFunc_8

[UScriptStruct::ICppStructOps]
__vecDelDtor
HasNoopConstructor
HasZeroConstructor
Construct
HasDestructor
Destruct
HasSerializer
Serialize
HasPostSerialize
PostSerialize
HasNetSerializer
HasNetSharedSerialization
NetSerialize
HasNetDeltaSerializer
NetDeltaSerialize
HasPostScriptConstruct
PostScriptConstruct
IsPlainOldData
HasCopy
Copy
HasIdentical
Identical
HasExportTextItem
ExportTextItem
HasImportTextItem
ImportTextItem
HasAddStructReferencedObjects
AddStructReferencedObjects
HasSerializeFromMismatchedTag
SerializeFromMismatchedTag
HasGetTypeHash
GetTypeHash
GetComputedPropertyFlags
IsAbstract

[UStruct]
__vecDelDtor
GetInheritanceSuper
Link
SerializeBin
SerializeTaggedProperties
InitializeStruct
DestroyStruct
SerializeExpr
GetPrefixCPP
SetSuperStruct
SerializeSuperStruct
PropertyNameToDisplayName
FindPropertyNameFromGuid
FindPropertyGuidFromName
ArePropertyGuidsAvailable

[FNumericProperty]
__vecDelDtor
IsFloatingPoint
IsInteger
GetIntPropertyEnum
SetIntPropertyValue
; void SetIntPropertyValue(param_count: 2, );
SetIntPropertyValue_2
SetFloatingPointPropertyValue
SetNumericPropertyValueFromString
GetSignedIntPropertyValue
GetUnsignedIntPropertyValue
GetFloatingPointPropertyValue
GetNumericPropertyValueToString
CanHoldDoubleValueInternal
CanHoldSignedValueInternal
CanHoldUnsignedValueInternal

[FMulticastDelegateProperty]
__vecDelDtor

[FProperty]
__vecDelDtor
GetCPPMacroType
PassCPPArgsByRef
GetCPPType
GetCPPTypeForwardDeclaration
LinkInternal
ConvertFromType
Identical
SerializeItem
NetSerializeItem
SupportsNetSharedSerialization
ExportTextItem
ImportText_Internal
CopyValuesInternal
GetValueTypeHashInternal
CopySingleValueToScriptVM
CopyCompleteValueToScriptVM
CopySingleValueFromScriptVM
CopyCompleteValueFromScriptVM
ClearValueInternal
DestroyValueInternal
InitializeValueInternal
GetID
InstanceSubobjects
GetMinAlignment
ContainsObjectReference
ContainsWeakObjectReference
EmitReferenceInfo
SameType

[FOutputDevice]
__vecDelDtor
Serialize
; void Serialize(param_count: 3, );
Serialize_2
Flush
TearDown
Dump
IsMemoryOnly
CanBeUsedOnAnyThread

[UField]
__vecDelDtor
AddCppProperty
Bind

[FMalloc]
__vecDelDtor
Malloc
Realloc
Free
QuantizeSize
GetAllocationSize
Trim
SetupTLSCachesOnCurrentThread
ClearAndDisableTLSCachesOnCurrentThread
InitializeStatsMetadata
UpdateStats
GetAllocatorStats
DumpAllocatorStats
IsInternallyThreadSafe
ValidateHeap
GetDescriptiveName

[FObjectPropertyBase]
__vecDelDtor
GetCPPTypeCustom
GetObjectPropertyValue
SetObjectPropertyValue
AllowCrossLevel
CheckValidObject

