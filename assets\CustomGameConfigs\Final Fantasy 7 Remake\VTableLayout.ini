[UObjectBase]
__vecDelDtor
RegisterDependencies
DeferredRegister

[UObjectBaseUtility]
__vecDelDtor
CanBeClusterRoot
CanBeInCluster
CreateCluster
OnClusterMarkedAsPendingKill
AddToCluster

[UObject]
__vecDelDtor
GetDetailedInfoInternal
PostInitProperties
PostCDOContruct
PreSaveRoot
PostSaveRoot
PreSave
Modify
PostLoad
PostLoadSubobjects
BeginDestroy
IsReadyForFinishDestroy
FinishDestroy
Serialize
ShutdownAfterError
PostInterpChange
PostRename
; void PostDuplicate(EDuplicateMode::Type)const;
PostDuplicate
; void PostDuplicate_1(bool)const;
PostDuplicate_1
NeedsLoadForClient
NeedsLoadForServer
NeedsLoadForEditorGame
IsEditorOnly
IsPostLoadThreadSafe
GetPreloadDependencies
GetPrestreamPackages
ExportCustomProperties
ImportCustomProperties
PostEditImport
PostReloadConfig
Rename
GetDesc
GetWorld
GetNativePropertyValues
GetResourceSize
GetResourceSizeEx
GetExporterName
GetRestoreForUObjectOverwrite
AreNativePropertiesIdenticalTo
GetAssetRegistryTags
IsAsset
NotifyObjectReferenceEliminated
GetPrimaryAssetId
IsLocalizedResource
IsSafeForRootSet
TagSubobjects
GetLifetimeReplicatedProps
IsNameStableForNetworking
IsFullNameStableForNetworking
IsSupportedForNetworking
GetSubobjectsWithStableNamesForNetworking
PreNetReceive
PostNetReceive
PostRepNotifies
PreDestroyFromReplication
BuildSubobjectMapping
GetConfigOverridePlatform
ProcessEvent
GetFunctionCallspace
CallRemoteFunction
ProcessConsoleExec
RegenerateClass
MarkAsEditorOnlySubobject
CheckDefaultSubobjectsInternal

[ITextData]
__vecDelDtor
OwnsLocalizedString
GetDisplayString
GetLocalizedString
GetMutableLocalizedString
GetTextHistory
GetMutableTextHistory
PersistText
GetGlobalHistoryRevision
GetLocalHistoryRevision

[FArchive]
__vecDelDtor
; FArchive* operator<<(FWeakObjectPtr*)const;
operator<<
; FArchive* operator<<_1(FSoftObjectPath*)const;
operator<<_1
; FArchive* operator<<_2(FSoftObjectPtr*)const;
operator<<_2
; FArchive* operator<<_3(FLazyObjectPtr*)const;
operator<<_3
; FArchive* operator<<_4(UObject**)const;
operator<<_4
; FArchive* operator<<_5(FText*)const;
operator<<_5
; FArchive* operator<<_6(FName*)const;
operator<<_6
ForceBlueprintFinalization
Serialize
SerializeBits
SerializeInt
SerializeIntPacked
Preload
CountBytes
GetArchiveName
GetLinker
Tell
TotalSize
AtEnd
Seek
AttachBulkData
DetachBulkData
Precache
FlushCache
SetCompressionMap
Flush
Close
GetError
MarkScriptSerializationStart
MarkScriptSerializationEnd
MarkSearchableName
GetArchetypeFromLoader
IndicateSerializationMismatch
GetCustomVersions
SetCustomVersions
ResetCustomVersions
IsCloseComplete
IsFilterEditorOnly
SetFilterEditorOnly
IsSaveGame
UseToResolveEnumerators
ShouldSkipProperty
AttachExternalReadDependency

[AActor]
__vecDelDtor
OnRep_ReplicateMovement
TearOff
HasNetOwner
OnRep_Owner
SetReplicateMovement
OnRep_AttachmentReplication
IsReplicationPausedForConnection
OnReplicationPausedChanged
ReplicateSubobjects
OnSubobjectCreatedFromReplication
OnSubobjectDestroyFromReplication
PreReplication
PreReplicationForReplay
OnRep_Instigator
EnableInput
DisableInput
GetVelocity
SetActorHiddenInGame
K2_DestroyActor
AddTickPrerequisiteActor
AddTickPrerequisiteComponent
RemoveTickPrerequisiteActor
RemoveTickPrerequisiteComponent
BeginPlay
NotifyActorBeginOverlap
NotifyActorEndOverlap
NotifyActorBeginCursorOver
NotifyActorEndCursorOver
NotifyActorOnClicked
NotifyActorOnReleased
NotifyActorOnInputTouchBegin
**************************
NotifyActorOnInputTouchEnter
NotifyActorOnInputTouchLeave
NotifyHit
SetLifeSpan
GetLifeSpan
GatherCurrentMovement
GetDefaultAttachComponent
ApplyWorldOffset
IsLevelBoundsRelevant
GetNetPriority
GetReplayPriority
GetNetDormancy
OnActorChannelOpen
UseShortConnectTimeout
OnSerializeNewActor
OnNetCleanup
TickActor
PostActorCreated
LifeSpanExpired
PostNetInit
OnRep_ReplicatedMovement
PostNetReceiveLocationAndRotation
PostNetReceiveVelocity
PostNetReceivePhysicState
SetOwner
CheckStillInWorld
abc
ab
Tick
ShouldTickIfViewportsOnly
IsNetRelevantFor
IsReplayRelevantFor
IsRelevancyOwnerFor
PreInitializeComponents
PostInitializeComponents
DispatchPhysicsCollisionHit
GetNetOwner
GetNetOwningPlayer
GetNetConnection
DestroyNetworkActorHandled
RegisterAllComponents
PostRegisterAllComponents
UnregisterAllComponents
PostUnregisterAllComponents
ReregisterAllComponents
MarkComponentsAsPendingKill
InvalidateLightingCacheDetailed
TeleportTo
TeleportSucceeded
ClearCrossLevelReferences
EndPlay
IsBasedOnActor
IsAttachedTo
RerunConstructionScripts
OnConstruction
RegisterActorTickFunctions
Destroyed
FellOutOfWorld
OutsideWorldBounds
GetComponentsBoundingBox
CalculateComponentsBoundingBoxInLocalSpace
GetComponentsBoundingCylinder
GetSimpleCollisionCylinder
IsRootComponentCollisionRegistered
TornOff
GetComponentsCollisionResponseToChannel
CanBeBaseForCharacter
TakeDamage
InternalTakeRadialDamage
InternalTakePointDamage
BecomeViewTarget
EndViewTarget
CalcCamera
HasActiveCameraComponent
HasActivePawnControlCameraComponent
GetHumanReadableName
Reset
GetLastRenderTime
ForceNetRelevant
ForceNetUpdate
PrestreamTextures
GetActorEyesViewPoint
GetTargetLocation
PostRenderFor
FindComponentByClass
IsComponentRelevantForNavigation
DisplayDebug

[ULocalPlayer]
__vecDelDtor
GetViewPoint
CalcSceneView
PlayerAdded
InitOnlineSession
PlayerRemoved
SpawnPlayActor
SendSplitJoin
SetControllerId
GetNickname
GetGameLoginOptions
GetProjectionData

[UEngine]
__vecDelDtor
WorldAdded
WorldDestroyed
IsInitialized
Init
Start
PreExit
ShutdownAudioDeviceManager
; TODO: Figure out where these were actually added.
Unk_0
Unk_1
Unk_2
Unk_3
Tick
GetMaxTickRate
GetMaxFPS
SetMaxFPS
UpdateRunningAverageDeltaTime
IsAllowedFramerateSmoothing
OnLostFocusPause
ShouldThrottleCPUUsage
IsHardwareSurveyRequired
OnHardwareSurveyComplete
ShouldDrawBrushWireframe
GetMapBuildCancelled
SetMapBuildCancelled
GetPropertyColorationColor
AllowSelectTranslucent
OnlyLoadEditorVisibleLevelsInPIE
PreferToStreamLevelsInPIE
GetSpriteCategoryIndex
StartFPSChart
StopFPSChart
ProcessToggleFreezeCommand
ProcessToggleFreezeStreamingCommand
IsSettingUpPlayWorld
GetGameViewportWidget
FocusNextPIEWorld
ResetPIEAudioSetting
GetNextPIEViewport
RemapGamepadControllerIdForPIE
NotifyToolsOfObjectReplacement
UseSound
CreatePIEWorldByDuplication
Experimental_ShouldPreDuplicateMap
InitializeAudioDeviceManager
InitializeHMDDevice
RecordHMDAnalytics
InitializeObjectReferences
InitializePortalServices
InitializeRunningAverageDeltaTime
SpawnServerActors
HandleNetworkFailure
HandleTravelFailure
HandleNetworkLagStateChanged
; bool NetworkRemapPath(UPendingNetGame*, FString*, bool)const;
NetworkRemapPath
; bool NetworkRemapPath_1(UNetDriver*, FString*, bool)const;
NetworkRemapPath_1
HandleOpenCommand
HandleTravelCommand
HandleStreamMapCommand
HandleServerTravelCommand
HandleSayCommand
HandleDisconnectCommand
HandleReconnectCommand
Browse
TickWorldTravel
LoadMap
RedrawViewports
TriggerStreamingDataRebuild
LoadMapRedrawViewports
CancelAllPending
; void CancelPending(UNetDriver*)const;
CancelPending
; void CancelPending_1(FWorldContext*)const;
CancelPending_1
; void CancelPending_2(UWorld*, UPendingNetGame*)const;
CancelPending_2
WorldIsPIEInNewViewport
VerifyLoadMapWorldCleanup
DestroyWorldContext
AreEditorAnalyticsEnabled
CreateStartupAnalyticsAttributes
IsAutosaving
ShouldDoAsyncEndOfFrameTasks
MovePendingLevel
ShouldShutdownWorldNetDriver
HandleBrowseToDefaultMapFailure
HandleNetworkFailure_NotifyGameInstance
HandleTravelFailure_NotifyGameInstance

[UScriptStruct::ICppStructOps]
__vecDelDtor
HasNoopConstructor
HasZeroConstructor
Construct
HasDestructor
Destruct
HasSerializer
Serialize
HasPostSerialize
PostSerialize
HasNetSerializer
NetSerialize
HasNetDeltaSerializer
NetDeltaSerialize
HasPostScriptConstruct
PostScriptConstruct
IsPlainOldData
HasCopy
Copy
HasIdentical
Identical
HasExportTextItem
ExportTextItem
HasImportTextItem
ImportTextItem
HasAddStructReferencedObjects
AddStructReferencedObjects
HasSerializeFromMismatchedTag
SerializeFromMismatchedTag
HasGetTypeHash
GetTypeHash
GetComputedPropertyFlags
IsAbstract

[UStruct]
__vecDelDtor
GetInheritanceSuper
Link
SerializeBin
SerializeTaggedProperties
InitializeStruct
DestroyStruct
SerializeExpr
GetPrefixCPP
SetSuperStruct
SerializeSuperStruct
PropertyNameToDisplayName
FindPropertyNameFromGuid
FindPropertyGuidFromName
ArePropertyGuidsAvailable

[FNumericProperty]
__vecDelDtor
IsFloatingPoint
IsInteger
GetIntPropertyEnum
; void SetIntPropertyValue(void*, int64)const;
SetIntPropertyValue
; void SetIntPropertyValue_1(void*, uint64)const;
SetIntPropertyValue_1
SetFloatingPointPropertyValue
SetNumericPropertyValueFromString
GetSignedIntPropertyValue
GetUnsignedIntPropertyValue
GetFloatingPointPropertyValue
GetNumericPropertyValueToString
CanHoldDoubleValueInternal
CanHoldSignedValueInternal
CanHoldUnsignedValueInternal

[FMulticastDelegateProperty]
__vecDelDtor

[UPlayer]
__vecDelDtor
SwitchController

[FProperty]
__vecDelDtor
GetCPPMacroType
PassCPPArgsByRef
GetCPPType
GetCPPTypeForwardDeclaration
LinkInternal
ConvertFromType
Identical
SerializeItem
NetSerializeItem
ExportTextItem
ImportText_Internal
CopyValuesInternal
GetValueTypeHashInternal
CopySingleValueToScriptVM
CopyCompleteValueToScriptVM
CopySingleValueFromScriptVM
CopyCompleteValueFromScriptVM
ClearValueInternal
DestroyValueInternal
InitializeValueInternal
GetID
InstanceSubobjects
GetMinAlignment
ContainsObjectReference
ContainsWeakObjectReference
EmitReferenceInfo
SameType

[AGameModeBase]
__vecDelDtor
InitializeHUDForPlayer_Implementation
InitStartSpot_Implementation
SpawnDefaultPawnAtTransform_Implementation
SpawnDefaultPawnFor_Implementation
PlayerCanRestart_Implementation
FindPlayerStart_Implementation
ChoosePlayerStart_Implementation
CanSpectate_Implementation
MustSpectate_Implementation
HandleStartingNewPlayer_Implementation
ShouldReset_Implementation
GetDefaultPawnClassForController_Implementation
InitGame
InitGameState
GetGameSessionClass
GetNumPlayers
GetNumSpectators
StartPlay
HasMatchStarted
SetPause
ClearPause
AllowPausing
IsPaused
ResetLevel
ReturnToMainMenuHost
CanServerTravel
ProcessServerTravel
GetSeamlessTravelActorList
SwapPlayerControllers
HandleSeamlessTravelPlayer
PostSeamlessTravel
StartToLeaveMap
GameWelcomePlayer
; void PreLogin(FString*, FString*, TSharedPtr<FUniqueNetId const ,0>*, FString*)const;
PreLogin
; void PreLogin_1(FString*, FString*, FUniqueNetIdRepl*, FString*)const;
PreLogin_1
; APlayerController* Login(UPlayer*, ENetRole, FString*, FString*, TSharedPtr<FUniqueNetId const ,0>*, FString*)const;
Login
; APlayerController* Login_1(UPlayer*, ENetRole, FString*, FString*, FUniqueNetIdRepl*, FString*)const;
Login_1
PostLogin
Logout
SpawnPlayerController
ChangeName
RestartPlayer
RestartPlayerAtPlayerStart
RestartPlayerAtTransform
SetPlayerDefaults
AllowCheats
IsHandlingReplays
SpawnPlayerFromSimulate
ShouldStartInCinematicMode
UpdateGameplayMuteList
; FString InitNewPlayer(APlayerController*, TSharedPtr<FUniqueNetId const ,0>*, FString*, FString*)const;
InitNewPlayer
; FString InitNewPlayer_1(APlayerController*, FUniqueNetIdRepl*, FString*, FString*)const;
InitNewPlayer_1
GenericPlayerInitialization
ReplicateStreamingStatus
ShouldSpawnAtStartSpot
FinishRestartPlayer
ProcessClientTravel
InitSeamlessTravelPlayer

[FOutputDevice]
__vecDelDtor
; void Serialize(wchar_t*, ELogVerbosity::Type, FName*, double)const;
Serialize
; void Serialize_1(wchar_t*, ELogVerbosity::Type, FName*)const;
Serialize_1
Flush
TearDown
Dump
IsMemoryOnly
CanBeUsedOnAnyThread

[UField]
__vecDelDtor
AddCppProperty
Bind

[UGameViewportClient]
__vecDelDtor
SSSwapControllers
ShowTitleSafeArea
SetConsoleTarget
Init
FinalizeViews
AddViewportWidgetContent
RemoveViewportWidgetContent
AddViewportWidgetForPlayer
RemoveViewportWidgetForPlayer
DetachViewportClient
Tick
SetViewportFrame
SetViewport
SetDropDetail
ConsoleCommand
SetupInitialLocalPlayer
UpdateActiveSplitscreenType
LayoutPlayers
GetSubtitleRegion
DrawTitleSafeArea
PostRender
DrawTransition
DrawTransitionMessage
NotifyPlayerAdded
NotifyPlayerRemoved
PeekTravelFailureMessages
PeekNetworkFailureMessages
VerifyPathRenderingComponents

[UDataTable]
__vecDelDtor

[FMalloc]
__vecDelDtor
Malloc
Realloc
Free
QuantizeSize
GetAllocationSize
Trim
SetupTLSCachesOnCurrentThread
ClearAndDisableTLSCachesOnCurrentThread
InitializeStatsMetadata
UpdateStats
GetAllocatorStats
DumpAllocatorStats
IsInternallyThreadSafe
ValidateHeap
GetDescriptiveName

[FObjectPropertyBase]
__vecDelDtor
GetCPPTypeCustom
GetObjectPropertyValue
SetObjectPropertyValue
AllowCrossLevel
CheckValidObject

[AGameMode]
__vecDelDtor
ReadyToEndMatch_Implementation
ReadyToStartMatch_Implementation
IsMatchInProgress
HasMatchEnded
StartMatch
EndMatch
RestartGame
AbortMatch
SetMatchState
OnMatchStateSet
HandleMatchIsWaitingToStart
HandleMatchHasStarted
HandleMatchHasEnded
HandleLeavingMap
HandleMatchAborted
GetNetworkNumber
GetDefaultGameClassPath
GetGameModeClass
GetTravelType
SendPlayer
StartNewPlayer
Say
SetBandwidthLimit
Broadcast
BroadcastLocalized
AddInactivePlayer
FindInactivePlayer
OverridePlayerState
SetSeamlessTravelViewTarget
MatineeCancelled
PreCommitMapChange
PostCommitMapChange
NotifyPendingConnectionLost
HandleDisconnect

