name: Deploy docs

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Setup mdBook Preprocessors
        uses: cargo-bins/cargo-binstall@v1.6.8

      - name: Setup mdBook
        run: |
          mkdir mdbook
          curl -sSL https://github.com/rust-lang/mdBook/releases/download/v0.4.14/mdbook-v0.4.14-x86_64-unknown-linux-gnu.tar.gz | tar -xz --directory=./mdbook
          echo `pwd`/mdbook >> $GITHUB_PATH

      - name: Install mdBook Preprocessors
        run: |
          cargo binstall --no-confirm mdbook-mermaid
          mdbook-mermaid install docs-export/
          cargo binstall --no-confirm mdbook-alerts

      - name: Build
        run: |
          python docs-export/export.py
          python docs-repo/build.py

      - uses: actions/upload-pages-artifact@v3
        with:
          path: docs-repo/docs
  deploy:
    needs: build

    permissions:
      pages: write
      id-token: write

    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    runs-on: ubuntu-latest
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
