[FExec]
__vecDelDtor
Exec

[UObjectBase]
__vecDelDtor
RegisterDependencies
DeferredRegister

[UObjectBaseUtility]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_2
CanBeClusterRoot
CanBeInCluster
CreateCluster
OnClusterMarkedAsPendingKill

[UObject]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_3
GetDetailedInfoInternal
PostInitProperties
PostCDOContruct
PreSaveRoot
PostSaveRoot
PreSave
IsReadyForAsyncPostLoad
PostLoad
PostLoadSubobjects
BeginDestroy
IsReadyForFinishDestroy
FinishDestroy
Serialize
; void Serialize(param_count: 1, FArchive& Ar);
Serialize_2
ShutdownAfterError
PostInterpChange
PostRename
PreDuplicate
PostDuplicate
; void PostDuplicate(param_count: 1, bool bDuplicateForPIE);
PostDuplicate_2
NeedsLoadForClient
NeedsLoadForServer
NeedsLoadForTargetPlatform
NeedsLoadForEditorGame
IsEditorOnly
IsPostLoadThreadSafe
IsDestructionThreadSafe
GetPreloadDependencies
GetPrestreamPackages
ExportCustomProperties
ImportCustomProperties
PostEditImport
PostReloadConfig
UnknownFunction_1
Rename
GetDesc
GetSparseClassDataStruct
GetWorld
GetNativePropertyValues
GetResourceSizeEx
GetExporterName
GetRestoreForUObjectOverwrite
AreNativePropertiesIdenticalTo
GetAssetRegistryTags
IsAsset
GetPrimaryAssetId
IsLocalizedResource
IsSafeForRootSet
TagSubobjects
GetLifetimeReplicatedProps
IsNameStableForNetworking
IsFullNameStableForNetworking
IsSupportedForNetworking
GetSubobjectsWithStableNamesForNetworking
PreNetReceive
PostNetReceive
PostRepNotifies
PreDestroyFromReplication
BuildSubobjectMapping
GetConfigOverridePlatform
OverridePerObjectConfigSection
ProcessEvent
GetFunctionCallspace
CallRemoteFunction
ProcessConsoleExec
RegenerateClass
MarkAsEditorOnlySubobject
CheckDefaultSubobjectsInternal
ValidateGeneratedRepEnums
SetNetPushIdDynamic
GetNetPushIdDynamic

[UScriptStruct::ICppStructOps]
__vecDelDtor
HasNoopConstructor
HasZeroConstructor
Construct
HasDestructor
Destruct
HasSerializer
HasStructuredSerializer
Serialize
; bool Serialize(param_count: 2, );
Serialize_2
HasPostSerialize
PostSerialize
HasNetSerializer
HasNetSharedSerialization
NetSerialize
HasNetDeltaSerializer
NetDeltaSerialize
HasPostScriptConstruct
PostScriptConstruct
IsPlainOldData
HasCopy
Copy
HasIdentical
Identical
HasExportTextItem
ExportTextItem
HasImportTextItem
ImportTextItem
HasAddStructReferencedObjects
AddStructReferencedObjects
HasSerializeFromMismatchedTag
HasStructuredSerializeFromMismatchedTag
SerializeFromMismatchedTag
StructuredSerializeFromMismatchedTag
HasGetTypeHash
GetStructTypeHash
GetComputedPropertyFlags
IsAbstract

[FArchive]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_2
operator<<
; FArchive& operator<<(param_count: 1, FSoftObjectPath& Value);
operator<<_2
; FArchive& operator<<(param_count: 1, FSoftObjectPtr& Value);
operator<<_3
; FArchive& operator<<(param_count: 1, FLazyObjectPtr& Value);
operator<<_4
; FArchive& operator<<(param_count: 1, FField& Value);
operator<<_5
; FArchive& operator<<(param_count: 1, UObject& Value);
operator<<_6
; FArchive& operator<<(param_count: 1, FText& Value);
operator<<_7
; FArchive& operator<<(param_count: 1, FName& Value);
operator<<_8
ForceBlueprintFinalization
Serialize
SerializeBits
SerializeInt
SerializeIntPacked
Preload
Seek
AttachBulkData
DetachBulkData
IsProxyOf
Precache
FlushCache
SetCompressionMap
Flush
Close
MarkScriptSerializationStart
MarkScriptSerializationEnd
MarkSearchableName
UsingCustomVersion
GetCacheableArchive
PushSerializedProperty
PopSerializedProperty
AttachExternalReadDependency
PushFileRegionType
PopFileRegionType

[FArchiveState]
__vecDelDtor
GetInnermostState
CountBytes
GetArchiveName
GetLinker
Tell
TotalSize
AtEnd
GetArchetypeFromLoader
GetCustomVersions
SetCustomVersions
ResetCustomVersions
SetFilterEditorOnly
UseToResolveEnumerators
ShouldSkipProperty
SetSerializedProperty
SetSerializedPropertyChain
SetSerializeContext
GetSerializeContext
Reset
SetIsLoading
SetIsSaving
SetIsTransacting
SetIsTextFormat
SetWantBinaryPropertySerialization
SetUseUnversionedPropertySerialization
SetForceUnicode
SetIsPersistent
SetUE4Ver
SetLicenseeUE4Ver
SetEngineVer
SetEngineNetVer
SetGameNetVer

[FOutputDevice]
__vecDelDtor
Serialize
; void Serialize(param_count: 3, );
Serialize_2
Flush
TearDown
Dump
IsMemoryOnly
CanBeUsedOnAnyThread
CanBeUsedOnMultipleThreads

[AGameModeBase]
__vecDelDtor
InitializeHUDForPlayer_Implementation
InitStartSpot_Implementation
SpawnDefaultPawnAtTransform_Implementation
SpawnDefaultPawnFor_Implementation
PlayerCanRestart_Implementation
FindPlayerStart_Implementation
ChoosePlayerStart_Implementation
CanSpectate_Implementation
MustSpectate_Implementation
HandleStartingNewPlayer_Implementation
ShouldReset_Implementation
GetDefaultPawnClassForController_Implementation
InitGame
InitGameState
GetGameSessionClass
GetNumPlayers
GetNumSpectators
StartPlay
HasMatchStarted
HasMatchEnded
SetPause
ClearPause
AllowPausing
IsPaused
ResetLevel
ReturnToMainMenuHost
CanServerTravel
ProcessServerTravel
GetSeamlessTravelActorList
SwapPlayerControllers
GetPlayerControllerClassToSpawnForSeamlessTravel
HandleSeamlessTravelPlayer
PostSeamlessTravel
StartToLeaveMap
GameWelcomePlayer
PreLogin
Login
PostLogin
Logout
SpawnPlayerController
; APlayerController* SpawnPlayerController(param_count: 2, ENetRole InRemoteRole, FString& Options);
SpawnPlayerController_2
SpawnReplayPlayerController
ChangeName
RestartPlayer
RestartPlayerAtPlayerStart
RestartPlayerAtTransform
SetPlayerDefaults
AllowCheats
IsHandlingReplays
SpawnPlayerFromSimulate
ShouldStartInCinematicMode
UpdateGameplayMuteList
InitNewPlayer
GenericPlayerInitialization
ReplicateStreamingStatus
ShouldSpawnAtStartSpot
FinishRestartPlayer
ProcessClientTravel
InitSeamlessTravelPlayer
SpawnPlayerControllerCommon

[UStruct]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_5
GetInheritanceSuper
Link
SerializeBin
; void SerializeBin(param_count: 2, FArchive& Ar, void* Data);
SerializeBin_2
SerializeTaggedProperties
; void SerializeTaggedProperties(param_count: 5, FArchive& Ar, uint8* Data, UStruct* DefaultsStruct, uint8* Defaults, UObject* BreakRecursionIfFullyLoad);
SerializeTaggedProperties_2
InitializeStruct
DestroyStruct
CustomFindProperty
SerializeExpr
GetPrefixCPP
SetSuperStruct
PropertyNameToDisplayName
GetAuthoredNameForField
; FString GetAuthoredNameForField(param_count: 1, UField* Field);
GetAuthoredNameForField_2
IsStructTrashed
FindPropertyNameFromGuid
FindPropertyGuidFromName
ArePropertyGuidsAvailable

[UField]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_4
AddCppProperty
Bind

[FMalloc]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_2
Malloc
TryMalloc
Realloc
TryRealloc
Free
QuantizeSize
GetAllocationSize
Trim
SetupTLSCachesOnCurrentThread
ClearAndDisableTLSCachesOnCurrentThread
InitializeStatsMetadata
UpdateStats
GetAllocatorStats
DumpAllocatorStats
IsInternallyThreadSafe
ValidateHeap
GetDescriptiveName

[AGameMode]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_2
ReadyToEndMatch_Implementation
ReadyToStartMatch_Implementation
IsMatchInProgress
StartMatch
EndMatch
RestartGame
AbortMatch
SetMatchState
OnMatchStateSet
HandleMatchIsWaitingToStart
HandleMatchHasStarted
HandleMatchHasEnded
HandleLeavingMap
HandleMatchAborted
GetNetworkNumber
GetDefaultGameClassPath
GetGameModeClass
GetTravelType
SendPlayer
StartNewPlayer
Say
SetBandwidthLimit
Broadcast
BroadcastLocalized
AddInactivePlayer
FindInactivePlayer
OverridePlayerState
SetSeamlessTravelViewTarget
MatineeCancelled
PreCommitMapChange
PostCommitMapChange
NotifyPendingConnectionLost
HandleDisconnect

[AActor]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_4
OnRep_ReplicateMovement
UnknownFunction_1
TearOff
UnknownFunction_2
UnknownFunction_3
HasNetOwner
HasLocalNetOwner
OnRep_Owner
SetReplicateMovement
OnRep_AttachmentReplication
IsReplicationPausedForConnection
OnReplicationPausedChanged
ReplicateSubobjects
OnSubobjectCreatedFromReplication
OnSubobjectDestroyFromReplication
PreReplication
PreReplicationForReplay
RewindForReplay
OnRep_Instigator
EnableInput
DisableInput
GetVelocity
UnknownFunction_4
UnknownFunction_5
UnknownFunction_6
UnknownFunction_7
UnknownFunction_8
SetActorHiddenInGame
K2_DestroyActor
AddTickPrerequisiteActor
AddTickPrerequisiteComponent
RemoveTickPrerequisiteActor
RemoveTickPrerequisiteComponent
BeginPlay
EndPlay
NotifyActorBeginOverlap
NotifyActorEndOverlap
NotifyActorBeginCursorOver
NotifyActorEndCursorOver
NotifyActorOnClicked
NotifyActorOnReleased
NotifyActorOnInputTouchBegin
**************************
NotifyActorOnInputTouchEnter
NotifyActorOnInputTouchLeave
NotifyHit
SetLifeSpan
GetLifeSpan
GatherCurrentMovement
GetDefaultAttachComponent
ApplyWorldOffset
IsLevelBoundsRelevant
GetNetPriority
GetReplayPriority
GetNetDormancy
OnActorChannelOpen
UseShortConnectTimeout
OnSerializeNewActor
OnNetCleanup
TickActor
PostActorCreated
LifeSpanExpired
PostNetReceiveRole
PostNetInit
OnRep_ReplicatedMovement
PostNetReceiveLocationAndRotation
PostNetReceiveVelocity
PostNetReceivePhysicState
SetOwner
CheckStillInWorld
Tick
ShouldTickIfViewportsOnly
IsNetRelevantFor
IsReplayRelevantFor
IsRelevancyOwnerFor
PreInitializeComponents
PostInitializeComponents
DispatchPhysicsCollisionHit
GetNetOwner
GetNetOwningPlayer
GetNetConnection
DestroyNetworkActorHandled
RegisterAllComponents
PreRegisterAllComponents
PostRegisterAllComponents
UnregisterAllComponents
PostUnregisterAllComponents
ReregisterAllComponents
MarkComponentsAsPendingKill
InvalidateLightingCacheDetailed
TeleportTo
TeleportSucceeded
UnknownFunction_9
UnknownFunction_10
ClearCrossLevelReferences
IsBasedOnActor
IsAttachedTo
RerunConstructionScripts
OnConstruction
RegisterActorTickFunctions
Destroyed
FellOutOfWorld
OutsideWorldBounds
GetComponentsBoundingBox
CalculateComponentsBoundingBoxInLocalSpace
GetComponentsBoundingCylinder
GetSimpleCollisionCylinder
IsRootComponentCollisionRegistered
UnknownFunction_11
UnknownFunction_12
UnknownFunction_13
UnknownFunction_14
UnknownFunction_15
TornOff
GetComponentsCollisionResponseToChannel
CanBeBaseForCharacter
TakeDamage
InternalTakeRadialDamage
InternalTakePointDamage
BecomeViewTarget
EndViewTarget
CalcCamera
HasActiveCameraComponent
HasActivePawnControlCameraComponent
GetHumanReadableName
Reset
GetLastRenderTime
ForceNetRelevant
ForceNetUpdate
PrestreamTextures
GetActorEyesViewPoint
GetTargetLocation
PostRenderFor
FindComponentByClass
UnknownFunction_16
IsComponentRelevantForNavigation
DisplayDebug

[AHUD]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_5
ShowHUD
ShowDebug
NotifyHitBoxClick
NotifyHitBoxRelease
NotifyHitBoxBeginCursorOver
NotifyHitBoxEndCursorOver
DrawActorOverlays
DrawSafeZoneOverlay
NotifyBindPostProcessEffects
RemovePostRenderedActor
AddPostRenderedActor
ShouldDisplayDebug
ShowDebugInfo
GetCurrentDebugTargetActor
GetDebugActorList
NextDebugTarget
PreviousDebugTarget
PostRender
DrawHUD
GetFontFromSizeIndex
OnLostFocusPause
HandleBugScreenShot

[IXRTrackingSystem]
GetVersionString
GetXRSystemFlags
DoesSupportPositionalTracking
DoesSupportLateUpdate
HasValidTrackingPosition
EnumerateTrackedDevices
CountTrackedDevices
IsTracking
RefreshPoses
RebaseObjectOrientationAndPosition
GetCurrentPose
GetRelativeEyePose
GetTrackingSensorProperties
GetTrackedDeviceType
GetTrackedDevicePropertySerialNumber
SetTrackingOrigin
GetTrackingOrigin
GetTrackingToWorldTransform
GetWorldToMetersScale
GetFloorToEyeTrackingTransform
UpdateTrackingToWorldTransform
GetAudioListenerOffset
ResetOrientationAndPosition
ResetOrientation
ResetPosition
SetBaseRotation
GetBaseRotation
SetBaseOrientation
GetBaseOrientation
SetBasePosition
GetBasePosition
CalibrateExternalTrackingSource
UpdateExternalTrackingPosition
GetXRCamera
GetHMDDevice
GetStereoRenderingDevice
GetXRInput
GetLoadingScreen
IsHeadTrackingAllowed
IsHeadTrackingAllowedForWorld
IsHeadTrackingEnforced
SetHeadTrackingEnforced
OnBeginPlay
OnEndPlay
OnStartGameFrame
OnEndGameFrame
OnBeginRendering_RenderThread
OnBeginRendering_GameThread
OnLateUpdateApplied_RenderThread
GetHMDData
GetMotionControllerData
ConfigureGestures
ConnectRemoteXRDevice
DisconnectRemoteXRDevice

[IXRCamera]
UseImplicitHMDPosition
GetUseImplicitHMDPosition
ApplyHMDRotation
UpdatePlayerCamera
OverrideFOV
SetupLateUpdate
CalculateStereoCameraOffset
GetPassthroughCameraUVs_RenderThread

[IHeadMountedDisplay]
__vecDelDtor
IsHMDConnected
IsHMDEnabled
GetHMDWornState
EnableHMD
GetHMDMonitorInfo
GetFieldOfView
SetClippingPlanes
GetEyeRenderParams_RenderThread
SetInterpupillaryDistance
GetInterpupillaryDistance
GetHMDDistortionEnabled
BeginRendering_RenderThread
BeginRendering_GameThread
IsSpectatorScreenActive
GetSpectatorScreenController
; ISpectatorScreenController* GetSpectatorScreenController();
GetSpectatorScreenController_2
CreateHMDPostProcessPass_RenderThread
GetPixelDenity
SetPixelDensity
GetIdealRenderTargetSize
GetIdealDebugCanvasRenderTargetSize
GetDistortionScalingFactor
GetLensCenterOffset
GetDistortionWarpValues
IsChromaAbCorrectionEnabled
GetChromaAbCorrectionValues
HasHiddenAreaMesh
HasVisibleAreaMesh
DrawHiddenAreaMesh_RenderThread
DrawVisibleAreaMesh_RenderThread
DrawDistortionMesh_RenderThread
UpdateScreenSettings
GetDistortionTextureLeft
GetDistortionTextureRight
GetTextureOffsetLeft
GetTextureOffsetRight
GetTextureScaleLeft
GetTextureScaleRight
GetRedDistortionParameters
GetGreenDistortionParameters
GetBlueDistortionParameters
NeedsUpscalePostProcessPass
RecordAnalytics
DoesAppUseVRFocus
DoesAppHaveVRFocus
IsRenderingPaused

[IHeadMountedDisplayModule]
__vecDelDtor
GetModuleKeyName
GetModuleAliases
PreInit
IsHMDConnected
GetGraphicsAdapterLuid
GetAudioInputDevice
GetAudioOutputDevice
CreateTrackingSystem
GetVulkanExtensions

[UPlayer]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_2
SwitchController

[ULocalPlayer]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_3
GetViewPoint
CalcSceneView
PlayerAdded
InitOnlineSession
PlayerRemoved
SpawnPlayActor
SendSplitJoin
SetControllerId
GetNickname
GetGameLoginOptions
GetProjectionData

[FField]
__vecDelDtor
Serialize
PostLoad
GetPreloadDependencies
BeginDestroy
AddReferencedObjects
AddCppProperty
Bind
PostDuplicate
GetInnerFieldByName
GetInnerFields

[FProperty]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_2
GetCPPMacroType
PassCPPArgsByRef
GetCPPType
GetCPPTypeForwardDeclaration
LinkInternal
ConvertFromType
Identical
SerializeItem
NetSerializeItem
SupportsNetSharedSerialization
ExportTextItem
ImportText_Internal
CopyValuesInternal
GetValueTypeHashInternal
CopySingleValueToScriptVM
CopyCompleteValueToScriptVM
CopySingleValueFromScriptVM
CopyCompleteValueFromScriptVM
ClearValueInternal
DestroyValueInternal
InitializeValueInternal
GetID
InstanceSubobjects
GetMinAlignment
ContainsObjectReference
EmitReferenceInfo
SameType

[FNumericProperty]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_3
IsFloatingPoint
IsInteger
GetIntPropertyEnum
SetIntPropertyValue
; void SetIntPropertyValue(param_count: 2, void* Data, uint64 Value);
SetIntPropertyValue_2
SetFloatingPointPropertyValue
SetNumericPropertyValueFromString
GetSignedIntPropertyValue
GetUnsignedIntPropertyValue
GetFloatingPointPropertyValue
GetNumericPropertyValueToString
CanHoldDoubleValueInternal
CanHoldSignedValueInternal
CanHoldUnsignedValueInternal

[FMulticastDelegateProperty]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_3
GetMulticastDelegate
SetMulticastDelegate
AddDelegate
RemoveDelegate
ClearDelegate
GetInvocationList

[FObjectPropertyBase]
; void* __vecDelDtor(param_count: 1, );
__vecDelDtor_3
GetCPPTypeCustom
LoadObjectPropertyValue
GetObjectPropertyValue
SetObjectPropertyValue
AllowCrossLevel
CheckValidObject

