[FDelegateProperty]
SignatureFunction = 0x70

[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20

[FChunkedFixedUObjectArray]
MaxChunks = 0x18
MaxElements = 0x10
NumChunks = 0x1C
NumElements = 0x14
Objects = 0x0
PreAllocatedObjects = 0x8

[FArchiveState]
ArAllowLazyLoading = 0x2A
ArContainsCode = 0x29
ArContainsMap = 0x29
ArCustomPropertyList = 0x60
ArEngineVer = 0x4C
ArForceByteSwapping = 0x2A
ArForceUnicode = 0x28
ArIgnoreArchetypeRef = 0x2A
ArIgnoreClassGeneratedByRef = 0x2A
ArIgnoreClassRef = 0x2A
ArIgnoreOuterRef = 0x2A
ArIsCountingMemory = 0x2B
ArIsCriticalError = 0x29
ArIsError = 0x29
ArIsFilterEditorOnly = 0x2B
ArIsLoading = 0x28
ArIsLoadingFromCookedPackage = 0x28
ArIsModifyingWeakAndStrongReferences = 0x2B
ArIsNetArchive = 0x2B
ArIsObjectReferenceCollector = 0x2B
ArIsPersistent = 0x29
ArIsSaveGame = 0x2B
ArIsSaving = 0x28
ArIsTextFormat = 0x28
ArIsTransacting = 0x28
ArLicenseeUEVer = 0x48
ArMaxSerializeSize = 0x38
ArNoDelta = 0x2A
ArNoIntraPropertyDelta = 0x2A
ArPortFlags = 0x30
ArRequiresLocalizationGather = 0x29
ArSerializingDefaults = 0x2C
ArShouldSkipBulkData = 0x2B
ArShouldSkipCompilingAssets = 0x29
ArShouldSkipUpdateCustomVersion = 0x29
ArUEVer = 0x40
ArUseCustomPropertyList = 0x2B
ArUseUnversionedPropertySerialization = 0x28
ArWantBinaryPropertySerialization = 0x28
CustomVersionContainer = 0x58
NextProxy = 0x88
SerializedProperty = 0x70
bCustomVersionsAreReset = 0x80

[FWorldContext]
AudioDeviceID = 0x240
ContextHandle = 0xA0
CustomDescription = 0x248
ExternalReferences = 0x2B0
GameViewport = 0x200
LevelsToLoadForPendingMapChange = 0x1A8
PIEAccumulatedTickSeconds = 0x25C
PIEFixedTickSeconds = 0x258
PIEInstance = 0x220
PIEPrefix = 0x228
PendingMapChangeFailureDescription = 0x1C8
RunAsDedicated = 0x23C
ThisCurrentWorld = 0x2C0
TravelType = 0xB8
TravelURL = 0xA8
bIsPrimaryPIEInstance = 0x23E
bShouldCommitPendingMapChange = 0x1D8
bWaitingOnOnlineSubsystem = 0x23D

[ULocalPlayer]
AspectRatioAxisConstraint = 0xB8
CachedUniqueNetId = 0x48
ControllerId = 0xE0
OnPlayerControllerChangedEvent = 0x120
PlatformUserId = 0x100
SlateOperations = 0x1F8
ViewportClient = 0x78
bSentSplitJoin = 0xC8

[FByteProperty]
Enum = 0x70

[FUObjectItem]
ClusterRootIndex = 0xC
Flags = 0x8
Object = 0x0
SerialNumber = 0x10

[FFixedUObjectArray]
MaxElements = 0x8
NumElements = 0xC
Objects = 0x0

[FBoolProperty]
ByteMask = 0x72
ByteOffset = 0x71
FieldMask = 0x73
FieldSize = 0x70

[FUObjectArray]
MaxObjectsNotConsideredByGC = 0x8
ObjAvailableList = 0x58
ObjFirstGCIndex = 0x0
ObjLastNonGCIndex = 0x4
ObjObjects = 0x10
OpenForDisregardForGC = 0xC
UObjectCreateListeners = 0x68
UObjectDeleteListeners = 0x78
bShouldRecycleObjectIndices = 0xB4

[FField]
ClassPrivate = 0x8
FlagsPrivate = 0x28
NamePrivate = 0x20
Next = 0x18
Owner = 0x10

[FMulticastDelegateProperty]
SignatureFunction = 0x70

[AGameModeBase]
DefaultPlayerName = 0x308
GameNetDriverReplicationSystem = 0x31C
GameSession = 0x2F0
GameSessionClass = 0x2A8
HUDClass = 0x2C8
OptionsString = 0x298
PlayerStateClass = 0x2C0
ServerStatReplicator = 0x300
ServerStatReplicatorClass = 0x2E8
SpectatorClass = 0x2D8
bPauseable = 0x318
bStartPlayersAsSpectators = 0x318
bUseSeamlessTravel = 0x318

[FSoftClassProperty]
MetaClass = 0x78

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[UEnum]
CppForm = 0x50
CppType = 0x30
EnumDisplayNameFn = 0x58
EnumFlags = 0x54
EnumPackage = 0x60
Names = 0x40

[UClass]
AllFunctionsCache = 0x180
ClassConfigName = 0xE8
ClassConstructor = 0xB0
ClassDefaultObject = 0x110
ClassFlags = 0xD4
ClassUnique = 0xC8
ClassVTableHelperCtorCaller = 0xB8
ClassWithin = 0xE0
FirstOwnedClassRep = 0xCC
FuncMap = 0x128
Interfaces = 0x1D8
NetFields = 0x100
ReferenceSchema = 0x1E8
SparseClassData = 0x118
SparseClassDataStruct = 0x120
bCooked = 0xD0
bLayoutChanging = 0xD1

[FSetProperty]
ElementProp = 0x70

[FInterfaceProperty]
InterfaceClass = 0x70

[UFunction]
EventGraphCallOffset = 0xD0
EventGraphFunction = 0xC8
FirstPropertyToInit = 0xC0
Func = 0xD8
FunctionFlags = 0xB0
NumParms = 0xB4
ParmsSize = 0xB6
RPCId = 0xBA
RPCResponseId = 0xBC
ReturnValueOffset = 0xB8

[UField]
Next = 0x28

[FProperty]
ArrayDim = 0x30
DestructorLinkNext = 0x58
ElementSize = 0x34
NextRef = 0x50
Offset_Internal = 0x44
PostConstructLinkNext = 0x60
PropertyFlags = 0x38
PropertyLinkNext = 0x48
RepIndex = 0x40
RepNotifyFunc = 0x68

[UStruct]
ChildProperties = 0x50
Children = 0x48
DestructorLink = 0x80
MinAlignment = 0x5C
PostConstructLink = 0x88
PropertiesSize = 0x58
PropertyLink = 0x70
RefLink = 0x78
Script = 0x60
ScriptAndPropertyObjectReferences = 0x90
SuperStruct = 0x40
UnresolvedScriptProperties = 0xA0

[FEnumProperty]
Enum = 0x78
UnderlyingProp = 0x70

[FMapProperty]
KeyProp = 0x70
MapFlags = 0x98
ValueProp = 0x78

[FClassProperty]
MetaClass = 0x78

[FObjectPropertyBase]
PropertyClass = 0x70

[UScriptStruct]
CppStructOps = 0xB8
StructFlags = 0xB0
bPrepareCppStructOpsCompleted = 0xB4

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[FArrayProperty]
ArrayFlags = 0x70
Inner = 0x78

[FStructProperty]
Struct = 0x70

[FFieldPathProperty]
PropertyClass = 0x70

[AActor]
ActorCategory = 0x69
ActorHasBegunPlay = 0x65
AttachmentReplication = 0x78
AutoReceiveInput = 0x15B
Children = 0x198
CreationTime = 0x160
CustomTimeDilation = 0x70
DefaultUpdateOverlapsMethodDuringLevelStreaming = 0x67
DetachFence = 0x290
InitialLifeSpan = 0x6C
InputComponent = 0x168
InputPriority = 0x15C
LastRenderTime = 0x188
Layers = 0x1B8
MinNetUpdateFrequency = 0x17C
NetCullDistanceSquared = 0x170
NetDormancy = 0x159
NetDriverName = 0x150
NetPriority = 0x180
NetPushId_Internal = 0x28
NetTag = 0x174
NetUpdateFrequency = 0x178
OnActorBeginOverlap = 0x1E3
OnActorEndOverlap = 0x1E4
OnActorHit = 0x1ED
OnBeginCursorOver = 0x1E5
OnClicked = 0x1E7
OnDestroyed = 0x1EE
OnEndCursorOver = 0x1E6
OnEndPlay = 0x1EF
OnInputTouchBegin = 0x1E9
OnInputTouchEnd = 0x1EA
OnInputTouchEnter = 0x1EB
OnInputTouchLeave = 0x1EC
OnReleased = 0x1E8
OnTakeAnyDamage = 0x1E0
OnTakePointDamage = 0x1E1
OnTakeRadialDamage = 0x1E2
Owner = 0x148
ParentComponent = 0x1C8
PhysicsReplicationMode = 0x184
PrimaryActorTick = 0x30
RayTracingGroupId = 0x74
RemoteRole = 0x68
ReplicatedComponentsInfo = 0x200
ReplicatedMovement = 0xD8
ReplicatedSubObjects = 0x1F0
Role = 0x158
RootComponent = 0x1A8
SpawnCollisionHandlingMethod = 0x15A
Tags = 0x1D0
TimerHandle_LifeSpanExpired = 0x1B0
UpdateOverlapsMethodDuringLevelStreaming = 0x66
bActorBeginningPlayFromLevelStreaming = 0x64
bActorEnableCollision = 0x64
bActorInitialized = 0x64
bActorIsBeingConstructed = 0x65
bActorIsBeingDestroyed = 0x65
bActorSeamlessTraveled = 0x63
bActorWantsDestroyDuringBeginPlay = 0x65
bAllowReceiveTickEventOnDedicatedServer = 0x63
bAllowTickBeforeBeginPlay = 0x62
bAlwaysRelevant = 0x60
bAsyncPhysicsTickEnabled = 0x65
bAutoDestroyWhenFinished = 0x62
bBlockInput = 0x62
bCallPreReplication = 0x60
bCallPreReplicationForReplay = 0x60
bCanBeDamaged = 0x62
bCanBeInCluster = 0x63
bCollideWhenPlacing = 0x62
bEnableAutoLODGeneration = 0x63
bExchangedRoles = 0x61
bFindCameraComponentWhenViewTarget = 0x62
bForceNetAddressable = 0x61
bGenerateOverlapEventsDuringLevelStreaming = 0x62
bHasDeferredComponentRegistration = 0x64
bHasFinishedSpawning = 0x64
bHasRegisteredAllComponents = 0x64
bHidden = 0x60
bIgnoresOriginShifting = 0x62
bIsEditorOnlyActor = 0x63
bNetCheckedInitialPhysicsState = 0x63
bNetLoadOnClient = 0x61
bNetStartup = 0x60
bNetTemporary = 0x60
bNetUseOwnerRelevancy = 0x61
bOnlyRelevantToOwner = 0x60
bRelevantForLevelBounds = 0x61
bRelevantForNetworkReplays = 0x61
bReplayRewindable = 0x61
bReplicateMovement = 0x60
bReplicateUsingRegisteredSubObjectList = 0x63
bReplicates = 0x63
bRunningUserConstructionScript = 0x64
bTearOff = 0x61
bTickFunctionsRegistered = 0x64

[UPlayer]
ConfiguredInternetSpeed = 0x3C
ConfiguredLanSpeed = 0x40
CurrentNetSpeed = 0x38

[UWorld]
ActiveLevelCollectionIndex = 0x198
AllLevelsChangedEvent = 0x540
AudioTimeSeconds = 0x6D8
AuthorityGameMode = 0x158
BlockTillLevelStreamingCompletedEpoch = 0x148
BuildStreamingDataTimer = 0x4A8
CleanupWorldTag = 0x76C
CommittedPersistentLevelName = 0x760
DeltaRealTimeSeconds = 0x6E0
DeltaTimeSeconds = 0x6E4
ExtraReferencedObjects = 0x68
IsInBlockTillLevelStreamingCompleted = 0x144
LastRenderTime = 0x130
LastTimeUnbuiltLightingWasEncountered = 0x6B8
NextSwitchCountdown = 0x720
NextURL = 0x740
NumStreamingLevelsBeingLoaded = 0x73A
OnBeginPlay = 0x1A0
PauseDelay = 0x6E8
PerModuleDataObjects = 0x78
PlayerNum = 0x678
PreparingLevelNames = 0x750
RealTimeSeconds = 0x6D0
StreamingLevelsPrefix = 0xC8
StreamingVolumeUpdateDelay = 0x67C
TimeSeconds = 0x6C0
UnpausedTimeSeconds = 0x6C8
bActorsInitialized = 0x13C
bAggressiveLOD = 0x13C
bAllowAudioPlayback = 0x13E
bAllowDeferredPhysicsStateCreation = 0x108
bAreConstraintsDirty = 0x13E
bBegunPlay = 0x13D
bDebugPauseExecution = 0x13D
bDoDelayedUpdateCullDistanceVolumes = 0x13C
bDropDetail = 0x13C
bHasEverBeenInitialized = 0x13F
bInTick = 0x13B
bIsBeingCleanedUp = 0x140
bIsBuilt = 0x13B
bIsCameraMoveableWhenPaused = 0x13E
bIsDefaultLevel = 0x13C
bIsLevelStreamingFrozen = 0x13B
bIsRunningConstructionScript = 0x13C
bIsTearingDown = 0x13D
bIsWorldInitialized = 0x13B
bKismetScriptError = 0x13D
bMarkedObjectsPendingKill = 0x768
bMatchStarted = 0x13D
bMaterialParameterCollectionInstanceNeedsDeferredUpdate = 0x13F
bPlayersOnly = 0x13D
bPlayersOnlyPending = 0x13D
bPostTickComponentUpdate = 0x13B
bRequestedBlockOnAsyncLoading = 0x13C
bRequiresHitProxies = 0x13E
bShouldForceUnloadStreamingLevels = 0x13E
bShouldForceVisibleStreamingLevels = 0x13E
bShouldSimulatePhysics = 0x13C
bShouldTick = 0x13E
bStartup = 0x13D
bStreamingDataDirty = 0x13E
bTickNewlySpawned = 0x13B
bTriggerPostLoadMap = 0x13B
bWorldWasLoadedThisTick = 0x13B

[UGameViewportClient]
ActiveSplitscreenType = 0x70
AudioDeviceHandle = 0xF8
CurrentBufferVisualizationMode = 0x130
CurrentGroomVisualizationMode = 0x150
CurrentLumenVisualizationMode = 0x140
CurrentNaniteVisualizationMode = 0x138
CurrentSubstrateVisualizationMode = 0x148
CurrentVirtualShadowMapVisualizationMode = 0x158
CursorWidgets = 0x210
DebugProperties = 0x48
EngineShowFlags = 0xB8
GameLayerManagerPtr = 0x120
HardwareCursorCache = 0x170
HardwareCursors = 0x1C0
HighResScreenshotDialog = 0x160
MaxSplitscreenPlayers = 0x68
MouseCaptureMode = 0x3AA
MouseLockMode = 0x3AC
SplitscreenInfo = 0x58
StatHitchesData = 0x3A0
StatUnitData = 0x398
ViewModeIndex = 0xB0
Viewport = 0xE8
ViewportConsole = 0x40
ViewportFrame = 0xF0
ViewportOverlayWidget = 0x110
Window = 0x100
World = 0x78
bDisableSplitScreenOverride = 0x3A8
bDisableWorldRendering = 0x6C
bHasAudioFocus = 0xFC
bHideCursorDuringCapture = 0x3AB
bIgnoreInput = 0x3A9
bIsMouseOverClient = 0x3AD
bIsPlayInEditorViewport = 0x6C
bSuppressTransitionMessage = 0x88
bUseSoftwareCursorWidgets = 0x260

[AGameMode]
EngineMessageClass = 0x350
InactivePlayerArray = 0x358
InactivePlayerStateLifeSpan = 0x368
MatchState = 0x330
MaxInactivePlayers = 0x36C
MinRespawnDelay = 0x348
NumBots = 0x344
NumPlayers = 0x340
NumSpectators = 0x33C
NumTravellingPlayers = 0x34C
bDelayedStart = 0x338
bHandleDedicatedServerReplays = 0x370

