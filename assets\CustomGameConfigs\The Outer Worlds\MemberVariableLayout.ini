[UObjectBase]
ClassPrivate = 0x10
InternalIndex = 0xC
NamePrivate = 0x18
ObjectFlags = 0x8
OuterPrivate = 0x20
; There's an extra 8 bytes at the end of UObjectBase.

[UScriptStruct::ICppStructOps]
Alignment = 0xC
Size = 0x8

[FProperty]
ArrayDim = 0x38
DestructorLinkNext = 0x68
ElementSize = 0x3C
NextRef = 0x60
Offset_Internal = 0x4C
PostConstructLinkNext = 0x70
PropertyFlags = 0x40
PropertyLinkNext = 0x58
RepIndex = 0x48
RepNotifyFunc = 0x50

[FSoftClassProperty]
MetaClass = 0x80

[FOutputDevice]
bAutoEmitLineTerminator = 0x9
bSuppressEventTag = 0x8

[UFunction]
EventGraphCallOffset = 0xB0
EventGraphFunction = 0xA8
FirstPropertyToInit = 0xA0
Func = 0xB8
FunctionFlags = 0x90
NumParms = 0x94
ParmsSize = 0x96
RPCId = 0x9A
RPCResponseId = 0x9C
ReturnValueOffset = 0x98

[UField]
Next = 0x30

[FEnumProperty]
Enum = 0x80
UnderlyingProp = 0x78

[UStruct]
Children = 0x40
DestructorLink = 0x70
MinAlignment = 0x4C
PostConstructLink = 0x78
PropertiesSize = 0x48
PropertyLink = 0x60
RefLink = 0x68
Script = 0x50
ScriptObjectReferences = 0x80
SuperStruct = 0x38

[FDelegateProperty]
SignatureFunction = 0x78

[FMulticastDelegateProperty]
SignatureFunction = 0x78

[FObjectPropertyBase]
PropertyClass = 0x78

[FBoolProperty]
ByteMask = 0x7A
ByteOffset = 0x79
FieldMask = 0x7B
FieldSize = 0x78

[UScriptStruct]
CppStructOps = 0x98
StructFlags = 0x90
bPrepareCppStructOpsCompleted = 0x94

; This struct has not been converted yet.
; You need to add +0x8 to each of these entries.
; UWorld is however not used for most UE4SS functionality so I'm not gonna bother with it for now.
[UWorld]
ActiveLevelCollectionIndex = 0x180
AudioDeviceHandle = 0x748
AudioTimeSeconds = 0x764
BuildStreamingDataTimer = 0x4A8
CommittedPersistentLevelName = 0x7D0
DebugDrawTraceTag = 0x6D8
DeltaTimeSeconds = 0x768
ExtraReferencedObjects = 0x68
LastTimeUnbuiltLightingWasEncountered = 0x750
NextSwitchCountdown = 0x7B8
NextURL = 0x7A8
NumLightingUnbuiltObjects = 0x7D8
NumTextureStreamingDirtyResources = 0x7E4
NumTextureStreamingUnbuiltComponents = 0x7E0
NumUnbuiltReflectionCaptures = 0x7DC
OriginOffsetThisFrame = 0x788
PauseDelay = 0x76C
PerModuleDataObjects = 0x78
PlayerNum = 0x6BC
PreparingLevelNames = 0x7C0
RealTimeSeconds = 0x760
StreamingLevelsPrefix = 0xE8
StreamingVolumeUpdateDelay = 0x6C4
TimeSeconds = 0x758
UnpausedTimeSeconds = 0x75C
ViewLocationsRenderedLastFrame = 0x120
bActorsInitialized = 0x7E8
bAggressiveLOD = 0x7E8
bAllowAudioPlayback = 0x7E8
bAreConstraintsDirty = 0x7E8
bBegunPlay = 0x7E8
bCleanedUpWorld = 0x7E8
bDebugDrawAllTraceTags = 0x6E0
bDebugFrameStepExecution = 0x7E8
bDebugPauseExecution = 0x7E8
bDoDelayedUpdateCullDistanceVolumes = 0x6CC
bDropDetail = 0x7E8
bHack_Force_UsesGameHiddenFlags_True = 0x6D4
bInTick = 0x5A8
bIsBuilt = 0x5A9
bIsCameraMoveableWhenPaused = 0x7E8
bIsDefaultLevel = 0x7E8
bIsLevelStreamingFrozen = 0x6C8
bIsRunningConstructionScript = 0x6D5
bIsTearingDown = 0x7E8
bIsWorldInitialized = 0x6C0
bKismetScriptError = 0x7E8
bMarkedObjectsPendingKill = 0x7E8
bMatchStarted = 0x7E8
bMaterialParameterCollectionInstanceNeedsDeferredUpdate = 0x6CB
bPlayersOnly = 0x7E8
bPlayersOnlyPending = 0x7E8
bPostTickComponentUpdate = 0x6B8
bRequestedBlockOnAsyncLoading = 0x7E8
bRequiresHitProxies = 0x3D8
bShouldForceUnloadStreamingLevels = 0x6C9
bShouldForceVisibleStreamingLevels = 0x6CA
bShouldSimulatePhysics = 0x6D6
bShouldTick = 0x3D9
bStartup = 0x7E8
bStreamingDataDirty = 0x4A0
bTickNewlySpawned = 0x5AA
bTriggerPostLoadMap = 0x130
bWorldWasLoadedThisTick = 0x130

[FSetProperty]
ElementProp = 0x78

[UClass]
ClassAddReferencedObjects = 0xB0
ClassConfigName = 0xE0
ClassConstructor = 0xA0
ClassDefaultObject = 0x108
ClassFlags = 0xBC
ClassGeneratedBy = 0xD0
ClassUnique = 0xB8
ClassVTableHelperCtorCaller = 0xA8
ClassWithin = 0xC8
Interfaces = 0x1B8
NetFields = 0xF8
UberGraphFramePointerProperty = 0xD8
bCooked = 0xB8

[UEnum]
CppForm = 0x58
CppType = 0x38
EnumDisplayNameFn = 0x60
Names = 0x48

[FMapProperty]
KeyProp = 0x78
ValueProp = 0x80

[FStructProperty]
Struct = 0x78

[FArrayProperty]
Inner = 0x78

[FByteProperty]
Enum = 0x78

[FClassProperty]
MetaClass = 0x80

[FInterfaceProperty]
InterfaceClass = 0x78

