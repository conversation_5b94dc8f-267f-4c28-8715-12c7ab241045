﻿[Overrides]
; Path to the 'Mods' folder
; Default: <dll_directory>/Mods
ModsFolderPath =

[General]
; Whether to reload all mods when the key defined by <PERSON>Reload<PERSON><PERSON> is hit.
; Default: 1
EnableHotReloadSystem = 0

; The key that will trigger a reload of all mods.
; The CTRL key is always required.
; Valid values (case-insensitive): Anything from Mods/Keybinds/Scripts/main.lua
; Default: R
HotReloadKey = R

; Whether caches will be invalidated if ue4ss.dll has changed
; Default: 1
InvalidateCacheIfDLLDiffers = 1

; The maximum number attempts the scanner will try before erroring out if an aob isn't found
; Default: 60
MaxScanAttemptsNormal = 60

; The maximum number attempts the scanner will try for modular games before erroring out if an aob isn't found
; Default: 2000
MaxScanAttemptsModular = 2500

; Whether to create UObject listeners in GUObjectArray to create a fast cache for use instead of iterating GUObjectArray.
; Setting this to false can help if you're experiencing a crash on startup.
; Default: true
bUseUObjectArrayCache = true

[EngineVersionOverride]
MajorVersion = 4
MinorVersion = 26

[ObjectDumper]
; Whether to force all assets to be loaded before dumping objects
; WARNING: Can require multiple gigabytes of extra memory
; WARNING: Is not stable & will crash the game if you load past the main menu after dumping
; Default: 0
LoadAllAssetsBeforeDumpingObjects = 0

; Whether to display the offset from the main executable for functions instead of the function pointer
; Default: 0
UseModuleOffsets = 0

[CXXHeaderGenerator]
; Whether to property offsets and sizes
; Default: 1
DumpOffsetsAndSizes = 1

; Whether memory layouts of classes and structs should be accurate
; This must be set to 1, if you want to use the generated headers in an actual C++ project
; When set to 0, padding member variables will not be generated
; NOTE: A VALUE OF 1 HAS NO PURPOSE YET! MEMORY LAYOUT IS NOT ACCURATE EITHER WAY!
; Default: 0
KeepMemoryLayout = 0

; Whether to force all assets to be loaded before generating headers
; WARNING: Can require multiple gigabytes of extra memory
; WARNING: Is not stable & will crash the game if you load past the main menu after dumping
; Default: 0
LoadAllAssetsBeforeGeneratingCXXHeaders = 0

[UHTHeaderGenerator]
; Whether to skip generating packages that belong to the engine
; Some games make alterations to the engine and for those games you might want to set this to 0
; Default: 0
IgnoreAllCoreEngineModules = 0

; Whether to skip generating the "Engine" and "CoreUObject" packages
; Default: 1
IgnoreEngineAndCoreUObject = 1

; Whether to force all UFUNCTION macros to have "BlueprintCallable"
; Note: This will cause some errors in the generated headers that you will need to manually fix
; Default: 1
MakeAllFunctionsBlueprintCallable = 1

; Whether to force all UPROPERTY macros to have "BlueprintReadWrite"
; Also forces all UPROPERTY macros to have "meta=(AllowPrivateAccess=true)"
; Default: 1
MakeAllPropertyBlueprintsReadWrite = 1

; Whether to force UENUM macros on enums to have 'BlueprintType' if the underlying type was implicit or uint8
; Note: This also forces the underlying type to be uint8 where the type would otherwise be implicit
; Default: 1
MakeEnumClassesBlueprintType = 1

; Whether to force "Config = Engine" on all UCLASS macros that use either one of:
; "DefaultConfig", "GlobalUserConfig" or "ProjectUserConfig"
; Default: 1
MakeAllConfigsEngineConfig = 1

[Debug]
; Whether to enable the external UE4SS debug console.
ConsoleEnabled = 0
GuiConsoleEnabled = 1
GuiConsoleVisible = 0

; Multiplier for Font Size within the Debug Gui
; Default: 1
GuiConsoleFontScaling = 1

; The API that will be used to render the GUI debug window.
; Valid values (case-insensitive): dx11, d3d11, opengl
; Default: opengl
GraphicsAPI = opengl

; How many objects to put in each group in the live view.
; A lower number means more groups but less lag when a group is open.
; A higher number means less groups but more lag when a group is open.
; Default: 32768
LiveViewObjectsPerGroup = 32768

[Threads]
; The number of threads that the sig scanner will use (not real cpu threads, can be over your physical & hyperthreading max)
; If the game is modular then multi-threading will always be off regardless of the settings in this file
; Min: 1
; Max: 4294967295
; Default: 8
SigScannerNumThreads = 8

; The minimum size that a module has to be in order for multi-threading to be enabled
; This should be large enough so that the cost of creating threads won't out-weigh the speed gained from scanning in multiple threads
; Min: 0
; Max: 4294967295
; Default: 16777216
SigScannerMultithreadingModuleSizeThreshold = 16777216

[Memory]
; The maximum memory usage (in percentage, see Task Manager %) allowed before asset loading (when LoadAllAssetsBefore* is 1) cannot happen.
; Once this percentage is reached, the asset loader will stop loading and whatever operation was in progress (object dump, or cxx generator) will continue.
; Default: 85
MaxMemoryUsageDuringAssetLoading = 85

[Hooks]
HookProcessInternal = 1
HookProcessLocalScriptFunction = 1
HookInitGameState = 1
HookCallFunctionByNameWithArguments = 1
HookBeginPlay  = 1
HookLocalPlayerExec = 1
FExecVTableOffsetInLocalPlayer = 0x28
